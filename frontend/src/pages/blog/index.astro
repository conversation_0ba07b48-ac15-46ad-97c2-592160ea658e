---
import Site from '../../layouts/Site.astro'
import Link from '../../components/elements/Link.astro'
import Main from '../../components/container/site/Main.astro'

import { Var } from '../../script/var'
import { currentPage, currentTopic, currentSubTopic } from '../../script/store'
currentPage.set('blog')
currentTopic.set('')
currentSubTopic.set('')

const title = `Blog | ${Var.app.name}`
const description =
  'Our blog explores how continuous feedback and research-based surveys can drive smarter business decisions and boost growth'
---

<Site title={title} description={description}>
  <Main>
    <header>
      <h2>Blog</h2>
      <a href=`${Var.app.website.url}/blog/test-blog-post/`>
        <div class="blog__item blog__item--wide spacing__vertical--200">
          <div>
            <h3>
              Lorem Ipsum is simply dummy text of the printing & typesetting
            </h3>
            <span class="footnote">14 JUNE 2024</span>
          </div>
        </div>
      </a>
      <div class="row row__justify--space-between spacing__vertical--300">
        <a href=`${Var.app.website.url}/blog/test-blog-post/`>
          <div class="blog__item">
            <div>
              <p>
                <strong
                  >Lorem Ipsum is simply dummy text of the printing &
                  typesetting
                </strong>
              </p>
              <span class="footnote">14 JUNE 2024</span>
            </div>
          </div>
        </a>
        <div class="display-on-mobile">
          <div class="spacing__vertical--200"></div>
        </div>
        <a href=`${Var.app.website.url}/blog/test-blog-post/`>
          <div class="blog__item">
            <div>
              <p>
                <strong
                  >Lorem Ipsum is simply dummy text of the printing &
                  typesetting
                </strong>
              </p>
              <span class="footnote">14 JUNE 2024</span>
            </div>
          </div>
        </a>
      </div>
    </header>
    <p>MORE POSTS</p>
    <ul class="list--no-bullet no-margin--left">
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/blog/test-blog-post/`
          >Lorem Ipsum is simply dummy text of the printing & typesetting<br />
          <span class="footnote">14 JUNE 2024</span></Link
        >
      </li>
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/blog/test-blog-post/`
          >Lorem Ipsum is simply dummy text of the printing & typesetting<br />
          <span class="footnote">14 JUNE 2024</span></Link
        >
      </li>
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/blog/test-blog-post/`
          >Lorem Ipsum is simply dummy text of the printing & typesetting<br />
          <span class="footnote">14 JUNE 2024</span></Link
        >
      </li>
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/blog/test-blog-post/`
          >Lorem Ipsum is simply dummy text of the printing & typesetting<br />
          <span class="footnote">14 JUNE 2024</span></Link
        >
      </li>
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/blog/test-blog-post/`
          >Lorem Ipsum is simply dummy text of the printing & typesetting<br />
          <span class="footnote">14 JUNE 2024</span></Link
        >
      </li>
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/blog/test-blog-post/`
          >Lorem Ipsum is simply dummy text of the printing & typesetting<br />
          <span class="footnote">14 JUNE 2024</span></Link
        >
      </li>
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/blog/test-blog-post/`
          >Lorem Ipsum is simply dummy text of the printing & typesetting<br />
          <span class="footnote">14 JUNE 2024</span></Link
        >
      </li>
    </ul>
  </Main>
</Site>

<style>
  a {
    display: contents;
  }

  .blog__item {
    width: 330px;
    display: flex;
    flex-direction: column-reverse;
    padding: calc(1.5 * var(--padding));
    border-radius: var(--border-radius);
    border: var(--border);
  }

  .blog__item:hover {
    -webkit-box-shadow: 0px 0px 20px 0px var(--color__grey--200);
    -moz-box-shadow: 0px 0px 20px 0px var(--color__grey--200);
    box-shadow: 0px 0px 20px 0px var(--color__grey--200);
  }

  .blog__item--wide {
    width: 100%;
    height: 300px;
  }

  @media only screen and (max-width: 480px) {
    .blog__item--narrow {
      width: 100%;
    }

    .blog__item--wide {
      width: 100%;
      height: 300px;
    }
  }
</style>
