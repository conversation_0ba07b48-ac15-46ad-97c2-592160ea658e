---
import Site from '../../../layouts/Site.astro'
import Link from '../../../components/elements/Link.astro'
import Main from '../../../components/container/site/Main.astro'

import { Var } from '../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../script/store'
currentPage.set('blog')
currentTopic.set('')
currentSubTopic.set('')

const title = `Test Blog Title | Blog — ${Var.app.name}`
const description = 'This is a test blog description'
---

<Site title={title} description={description}>
  <Main>
    <Link href=`${Var.app.website.url}/blog/`>← Back</Link>
    <h2 class="spacing__vertical--50">Test Blog Title</h2>
    <div class="separator spacing__vertical--50"></div>
    <div class="row row__justify--space-between">
      <div id="blog__author__container" class="row">
        <div id="blog__author__dp"></div>
        &nbsp;&nbsp;
        <Link href=`${Var.app.website.url}/blog/`>Tuhin Bhuyan</Link>
      </div>
      <p class="spacing__vertical--100">Updated on 14 June 2024</p>
    </div>
    <div class="separator spacing__vertical--50"></div>
    <p class="text--light spacing__vertical--200">
      Neque porro quisquam est qui dolorem ipsum quia dolor sit amet,
      consectetur, adipisci velit... quisquam est qui dolorem sit amet
    </p>
    <h3>Contents</h3>
    <ul>
      <li class="spacing__vertical--100">
        <Link href="#topic-1">This is Topic 1</Link>
      </li>
      <li class="spacing__vertical--100">
        <Link href="#topic-2">This is Topic 2</Link>
      </li>
      <li class="spacing__vertical--100">
        <Link href="#topic-3">This is Topic 3 </Link>
      </li>
      <li class="spacing__vertical--100">
        <Link href="#topic-4">This is Topic 4</Link>
      </li>
      <li class="spacing__vertical--100">
        <Link href="#topic-5">This is Topic 5</Link>
      </li>
    </ul>
    <div id="topic-1" class="spacing__vertical--200"></div>
    <p>
      <strong>This is topic 1</strong>
    </p>
    <p>
      Lorem Ipsum is simply dummy text of the printing and typesetting industry.
      Lorem Ipsum has been the industry's standard dummy text ever since the
      1500s, when an unknown printer took a galley of type and scrambled it to
      make a type specimen book.
    </p>
    <p>
      It has survived not only five centuries, but also the leap into electronic
      typesetting, remaining essentially unchanged.
    </p>
    <p>
      It was popularised in the 1960s with the release of Letraset sheets
      containing Lorem Ipsum passages, and more recently with desktop publishing
      software like Aldus PageMaker including versions of Lorem Ipsum.
    </p>
    <div id="topic-2" class="spacing__vertical--200"></div>
    <p>
      <strong>This is topic 2</strong>
    </p>
    <p>
      Lorem Ipsum is simply dummy text of the printing and typesetting industry.
      Lorem Ipsum has been the industry's standard dummy text ever since the
      1500s, when an unknown printer took a galley of type and scrambled it to
      make a type specimen book.
    </p>
    <p>
      It has survived not only five centuries, but also the leap into electronic
      typesetting, remaining essentially unchanged.
    </p>
    <p>
      It was popularised in the 1960s with the release of Letraset sheets
      containing Lorem Ipsum passages, and more recently with desktop publishing
      software like Aldus PageMaker including versions of Lorem Ipsum.
    </p>
    <div id="topic-3" class="spacing__vertical--200"></div>
    <p>
      <strong>This is topic 3</strong>
    </p>
    <p>
      Lorem Ipsum is simply dummy text of the printing and typesetting industry.
      Lorem Ipsum has been the industry's standard dummy text ever since the
      1500s, when an unknown printer took a galley of type and scrambled it to
      make a type specimen book.
    </p>
    <p>
      It has survived not only five centuries, but also the leap into electronic
      typesetting, remaining essentially unchanged.
    </p>
    <p>
      It was popularised in the 1960s with the release of Letraset sheets
      containing Lorem Ipsum passages, and more recently with desktop publishing
      software like Aldus PageMaker including versions of Lorem Ipsum.
    </p>
    <div id="topic-4" class="spacing__vertical--200"></div>
    <p>
      <strong>This is topic 4</strong>
    </p>
    <p>
      Lorem Ipsum is simply dummy text of the printing and typesetting industry.
      Lorem Ipsum has been the industry's standard dummy text ever since the
      1500s, when an unknown printer took a galley of type and scrambled it to
      make a type specimen book.
    </p>
    <p>
      It has survived not only five centuries, but also the leap into electronic
      typesetting, remaining essentially unchanged.
    </p>
    <p>
      It was popularised in the 1960s with the release of Letraset sheets
      containing Lorem Ipsum passages, and more recently with desktop publishing
      software like Aldus PageMaker including versions of Lorem Ipsum.
    </p>
    <div id="topic-5" class="spacing__vertical--200"></div>
    <p>
      <strong>This is topic 5</strong>
    </p>
    <p>
      Lorem Ipsum is simply dummy text of the printing and typesetting industry.
      Lorem Ipsum has been the industry's standard dummy text ever since the
      1500s, when an unknown printer took a galley of type and scrambled it to
      make a type specimen book.
    </p>
    <p>
      It has survived not only five centuries, but also the leap into electronic
      typesetting, remaining essentially unchanged.
    </p>
    <p>
      It was popularised in the 1960s with the release of Letraset sheets
      containing Lorem Ipsum passages, and more recently with desktop publishing
      software like Aldus PageMaker including versions of Lorem Ipsum.
    </p>
  </Main>
</Site>

<style>
  #blog__author__dp {
    height: 50px;
    width: 50px;
    border: var(--border);
    border-radius: 100%;
  }

  @media only screen and (max-width: 480px) {
    #blog__author__container {
      display: flex;
      align-items: center;
    }
  }
</style>
