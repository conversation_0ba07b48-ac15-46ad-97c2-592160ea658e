---
import Site from '../../../../../layouts/Site.astro'
import Link from '../../../../../components/elements/Link.astro'

import { Var } from '../../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../../script/store'
import Main from '../../../../../components/container/site/Main.astro'
currentPage.set('')
currentTopic.set('privacyPolicy')
currentSubTopic.set('gdprDpa')

const title = `GDPR DPA | Privacy Policy — ${Var.app.name}`
const description =
  'Learn how SenseFolks complies with the GDPR DPA. Understand your rights regarding data privacy, how we handle your personal information, and the measures we take to protect your privacy'
---

<Site title={title} description={description}>
  <Main>
    <header>
      <h2>GDPR DPA</h2>
      <p class="no-margin--bottom spacing__vertical--150">
        <u>Updated on August 18, 2024</u>. These policies is adapted from the <Link
          href="https://github.com/basecamp/policies"
          target="_blank">Basecamp Open-Source Policies</Link
        > / <Link
          href="https://creativecommons.org/licenses/by/4.0/"
          target="_blank">CC BY 4.0</Link
        >
      </p>
    </header>
  </Main>
</Site>
