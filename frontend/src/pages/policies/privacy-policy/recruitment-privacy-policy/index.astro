---
import Site from '../../../../layouts/Site.astro'
import Link from '../../../../components/elements/Link.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
import Main from '../../../../components/container/site/Main.astro'
currentPage.set('')
currentTopic.set('recruitmentPrivacyPolicy')
currentSubTopic.set('')

const title = `Recuitment Privacy Policy | Policies — ${Var.app.name}`
const description =
  'Learn how SenseFolks collects, uses, and protects personal data during the recruitment process. Understand your privacy rights and how we handle your information securely'
---

<Site title={title} description={description}>
  <Main>
    <header>
      <h2>Recuitment Privacy Policy</h2>
      <p class="no-margin--bottom spacing__vertical--150">
        <u>Updated on August 18, 2024</u>. These policies is adapted
        from the <Link
          href="https://github.com/basecamp/policies"
          target="_blank">Basecamp Open-Source Policies</Link
        > / <Link
          href="https://creativecommons.org/licenses/by/4.0/"
          target="_blank">CC BY 4.0</Link
        >
      </p>
    </header>
    <p>
      <Link href={Var.app.website.url} target="_blank">{Var.app.name}</Link>
      is a Software-as-a-Service (SaaS) developed by <Link
        href={Var.app.owner.website.url}
        target="_blank">{Var.app.owner.name}</Link
      >, India.
    </p>
    <p class="spacing__vertical--100">
      {Var.app.owner.name} conducts hiring via email only. When you apply for a job
      at
      {Var.app.owner.name}, we store your data in <Link
        href="https://workspace.google.com/"
        target="_blank">Google Workspace</Link
      >, our email service provider . If you progress to later selection stages,
      your information is co-located within the services offered by <Link
        href="https://workspace.google.com/"
        target="_blank">Google Workspace</Link
      >. Candidate data we collect are used for selection purposes only, and
      access to your personal information is limited to {Var.app.owner.name} employees
      who participate in the selection process.
    </p>
    <section class="spacing__vertical--300">
      <h3>What we collect, where we store it, and how long we keep it</h3>
      <div class="spacing__vertical--100">
        We collect the following details for the purpose of hiring:
        <ol>
          <li class="spacing__vertical--100">
            Name, email address, country of residence and contact number of the
            aplicant
          </li>
          <li class="spacing__vertical--100">
            Document relevant to the application process e.g. cover letter,
            CV/resume etc.
          </li>
          <li class="spacing__vertical--100">
            Any work completed as a part of the selection process e.g. skill
            assessment tests or sample project work.
          </li>
          <li class="spacing__vertical--100">
            Internal assessment notes about the application and interviews.
          </li>
          <li class="spacing__vertical--100">
            Short answer questions required in the application.
          </li>
          <li class="spacing__vertical--100">
            Reference contact details in case of reference checks.
          </li>
        </ol>
      </div>
      <p class="spacing__vertical--100">
        We store your data in <Link
          href="https://workspace.google.com/"
          target="_blank">Google Workspace</Link
        > until we successfully fill the open position. We may keep your records
        for up to two years if you advance to the interview stages of our hiring
        process. We do this to maintain a pool of recent, qualified applicants, should
        the position re-open or should we decide to hire for another similar role.
        You can ask us to access, correct, update, or delete your data at any time
        by emailing us at <Link
          href=`mailto:${Var.app.support.email}`
          target="_blank">{Var.app.support.email}</Link
        >
      </p>
      <p class="spacing__vertical--100">
        We’re unable to accept unsolicited job applications. If we receive one,
        we’ll delete it, and any personal information included,
      </p>
      <p class="spacing__vertical--100">
        Our recruitment privacy practices are GDPR compliant. We collect and
        store this data because we have a legitimate interest: we are trying to
        hire for a position, and we need information from any candidate who
        applies for the role. For more information about your rights to your
        data, please view our general <Link
          href=`${Var.app.website.url}/policies/privacy-policy/`
          target="_blank">Privacy Policy</Link
        >. All the rights outlined apply to your data as a candidate.
      </p>
      <p class="spacing__vertical--100">
        If you are in the EU, you can identify your specific authority to file a
        GDPR complaint at <Link
          href="https://edpb.europa.eu/about-edpb/board/members_en"
          target="_blank">edpb.europa.eu/about-edpb/board/members_en</Link
        >.
      </p>
      <p class="spacing__vertical--100">
        Complete information about how Google Workspace responsibly stores your
        data can be found in their <Link
          href="https://policies.google.com/privacy"
          target="_blank">privacy policy</Link
        >. Complete information about how we protect all data stored within
        SenseFolks can be found in our <Link
          href="https://policies.google.com/privacy"
          target="_blank">Privacy Policy</Link
        > and <Link href=`${Var.app.website.url}/policies/security-overview/` target="_blank">Security Overview</Link>.
      </p>
      <p>
        If you have questions about your personal data as a job applicant, you
        can contact us at <Link
          href=`mailto:${Var.app.support.email}`
          target="_blank">{Var.app.support.email}</Link
        >
      </p>
    </section>
    <div class="separator spacing__vertical--200"></div>
    <p>
      By signing up and using {Var.app.name} you automatically accept the <Link
        href=`${Var.app.website.url}/policies/terms-of-service/`
        target="_blank">Terms of Service</Link
      >, <Link href=`${Var.app.website.url}/policies/privacy-policy/` target="_blank"
        >Privacy Policy</Link
      > and rest of the policies. If you have a question about any of these Terms, please <Link
          href=`${Var.app.website.url}/support/`
          target="_blank">contact us</Link
        >
    </p>
  </Main>
</Site>
