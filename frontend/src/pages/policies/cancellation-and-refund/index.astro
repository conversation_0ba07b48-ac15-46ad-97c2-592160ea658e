---
import Site from '../../../layouts/Site.astro'
import Link from '../../../components/elements/Link.astro'

import { Var } from '../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../script/store'
import Main from '../../../components/container/site/Main.astro'
currentPage.set('')
currentTopic.set('cancellationAndRefund')
currentSubTopic.set('')

const title = `Cancellation & Refund | Policies — ${Var.app.name}`
const description =
  "Learn about SenseFolks' cancellation and refund policies. Understand the steps to cancel your subscription and how refunds are processed to ensure a smooth experience"
---

<Site title={title} description={description}>
  <Main>
    <header>
      <h2>Cancellation & Refund</h2>
      <p class="no-margin--bottom spacing__vertical--150">
        <u>Updated on August 18, 2024</u>. These policies is adapted from the <Link
          href="https://github.com/basecamp/policies"
          target="_blank">Basecamp Open-Source Policies</Link
        > / <Link
          href="https://creativecommons.org/licenses/by/4.0/"
          target="_blank">CC BY 4.0</Link
        >
      </p>
    </header>
    <p>
      We want satisfied customers, not hostages. That’s why we make it easy for
      you to cancel your {Var.app.name} account directly.
    </p>
    <div class="separator spacing__vertical--200"></div>
    <p>CONTENTS</p>
    <ul>
      <li class="spacing__vertical--100">
        <Link href="#cancellation">Cancellation</Link>
      </li>
      <li class="spacing__vertical--100">
        <Link href="#refunds">Refunds</Link>
      </li>
    </ul>
    <div id="cancellation" class="separator spacing__vertical--200"></div>
    <h3 class="spacing__vertical--100">Cancellation</h3>
    <p>
      Account owners can follow the following steps to cancel their account:
    </p>
    <ul>
      <li class="spacing__vertical--100">
        Click <u>Your Name</u> on the top right of the screen
      </li>
      <li class="spacing__vertical--100">
        Choose <u>Accounts</u> from the dropdown
      </li>
      <li class="spacing__vertical--100">
        Click <u>Delete Account</u>
      </li>
      <li class="spacing__vertical--100">Confirm account deletion</li>
    </ul>
    <p class="spacing__vertical--100">
      <strong>What happens when you cancel an account?</strong>
    </p>
    <p class="spacing__vertical--100">
      You won’t be able to access your account once you cancel, so make sure you
      download everything you want to keep beforehand. Then the account will be
      automatically canceled and will become inaccessible.
    </p>
    <p class="spacing__vertical--100">
      We’ll permanently delete the content in your account from our servers 30
      days after cancellation, and from our backups within 60 days. Retrieving
      content for a single account from a backup isn’t possible, so if you
      change your mind you’ll need to do it within the first 30 days after
      cancellation. <u
        >Content & data can’t be recovered once it has been permanently deleted</u
      >. We won’t bill you again once you cancel.
    </p>
    <p class="spacing__vertical--100">
      <strong>{Var.app.name}-initiated cancellations</strong>
    </p>
    <p class="spacing__vertical--100">
      We may cancel accounts if they have been inactive for an extended period:
    </p>
    <ol>
      <li class="spacing__vertical--100">
        <u>For trial accounts</u><br /> 60 days after a trial has expired without
        being upgraded
      </li>
      <li class="spacing__vertical--100">
        <u>For frozen accounts</u><br /> 180 days after being frozen due to billing
        failures
      </li>
      <li class="spacing__vertical--100">
        <u>For free accounts</u><br /> After 365 days of inactivity
      </li>
    </ol>
    <p>
      We also retain the right to suspend or terminate accounts for any reason
      at any time, as outlined in our <Link
        href=`${Var.app.website.url}/policies/terms-of-service/`
        >Terms of Service</Link
      >. In practice, this generally means we will cancel your account without
      notice if we have evidence that you are violating the <Link
        href=`${Var.app.website.url}/policies/terms-of-service/#user-restriction-list`
        >restricted use agreement</Link
      >.
    </p>
    <div id="your-data-are-sent-using-https" class="spacing__vertical--300">
    </div>

    <h3 class="spacing__vertical--100">Refunds</h3>
    <p class="spacing__vertical--100">
      If you’re ever unhappy with {Var.app.name} for any reason, contact our support
      team at <Link href=`mailto:${Var.app.support.email}` target="_blank"
        >{Var.app.support.email}</Link
      >
    </p>
    <p class="spacing__vertical--100">
      We sell both monthly and yearly subscriptions. If you pay for a year and
      then cancel before the year is up, we make sure you aren’t charged in the
      future. Your account will be accessible until your paid period ends. After
      that, your account will become inaccessible but your data will stay safe
      with us for 60 days. That gives you time to migrate to a new provider,
      export your data, and change any logins. After 60 days, we’ll permanently
      delete all data from the account. For more details, please read our <Link
        href=`${Var.app.website.url}/policies/cancellation-and-refund/`
        target="_blank">Cancellation Policy</Link
      >.
    </p>
    <p class="spacing__vertical--100">
      <strong>Examples of full/partial refunds we’d grant:</strong>
    </p>
    <ul>
      <li class="spacing__vertical--100">
        <p>
          If you were just charged for your next month of service but you meant
          to cancel, please inform us within 7 days of being charged. We will
          happily initiate a refund.
        </p>
      </li>
      <li class="spacing__vertical--100">
        <p>
          If you upgraded your account a few months ago to a higher plan and
          kept using it in general but you didn’t end up using the extra
          features, we’d consider applying a prorated credit towards future
          months.
        </p>
      </li>
      <li class="spacing__vertical--100">
        <p>
          If we had extended downtime (multiple hours in a day, or multiple days
          in a month) or you emailed customer service and it took multiple days
          to get back to you, we’d issue a partial credit to your account.
        </p>
      </li>
    </ul>
    <div class="separator spacing__vertical--200"></div>
    <p>
      By signing up and using {Var.app.name} you automatically accept the <Link
        href=`${Var.app.website.url}/policies/terms-of-service/`
        target="_blank">Terms of Service</Link
      >, <Link
        href=`${Var.app.website.url}/policies/privacy-policy/`
        target="_blank">Privacy Policy</Link
      > and rest of the policies. If you have a question about any of these Terms,
      please <Link href=`${Var.app.website.url}/support/` target="_blank"
        >contact us</Link
      >
    </p>
  </Main>
</Site>
