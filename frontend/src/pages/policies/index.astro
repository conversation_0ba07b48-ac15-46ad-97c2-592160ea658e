---
import Site from '../../layouts/Site.astro'
import Link from '../../components/elements/Link.astro'

import { Var } from '../../script/var'
import { currentPage, currentTopic, currentSubTopic } from '../../script/store'
import Main from '../../components/container/site/Main.astro'
currentPage.set('policies')
currentTopic.set('')
currentSubTopic.set('')

const title = `Policies | ${Var.app.name} `
const description =
  'Access all SenseFolks policies in one place. Stay informed about how we protect your data and ensure compliance with industry standards'
---

<Site title={title} description={description}>
  <Main>
    <header>
      <h2>All Policies</h2>
      <p class="no-margin--bottom spacing__vertical--150">
        <u>Updated on August 18, 2024</u>. These policies is adapted from the <Link
          href="https://github.com/basecamp/policies"
          target="_blank">Basecamp Open-Source Policies</Link
        > / <Link
          href="https://creativecommons.org/licenses/by/4.0/"
          target="_blank">CC BY 4.0</Link
        >
      </p>
    </header>
    <p>
      We make our policies as clear, fair, and readable as possible. We may
      update these policies as needed to comply with relevant regulations and
      reflect any new practices. If we make significant changes, we will refresh
      the date at the top of the page. As a customer, kindly go through the
      policies before using Sensfolks.
    </p>
    <div class="separator spacing__vertical--200"></div>
    <p>CONTENTS</p>
    <ul>
      <li class="spacing__vertical--100">
        <Link href=`${Var.app.website.url}/policies/terms-of-service/`
          >Terms of Service</Link
        >
      </li>
      <li class="spacing__vertical--100">
        <Link href=`${Var.app.website.url}/policies/cancellation-and-refund/`
          >Cancellation & Refund</Link
        >
      </li>
      <li class="spacing__vertical--100">
        <Link href=`${Var.app.website.url}/policies/privacy-policy/`
          >Privacy Policy</Link
        >
        <ul class="no-margin--bottom">
          <li>
            <Link href=`${Var.app.website.url}/policies/privacy-policy/ccpa/`
              >CCPA</Link
            >
          </li>
          <li>
            <Link href=`${Var.app.website.url}/policies/privacy-policy/gdpr/`
              >GDPR DPA</Link
            >
          </li>
          <li>
            <Link
              href=`${Var.app.website.url}/policies/privacy-policy/data-processors/`
              >Data Processors</Link
            >
          </li>
          <li>
            <Link
              href=`${Var.app.website.url}/policies/privacy-policy/data-subprocessors/`
              >Data Subprocessors</Link
            >
          </li>
        </ul>
      </li>
      <li class="spacing__vertical--100">
        <Link
          href=`${Var.app.website.url}/policies/privacy-policy/recruitment-privacy-policy/`
          >Recruitment Privacy Policy</Link
        >
      </li>
      <li class="spacing__vertical--100">
        <Link href=`${Var.app.website.url}/policies/security-overview/`
          >Security Overview</Link
        >
      </li>
    </ul>
    <div class="separator spacing__vertical--200"></div>
    <p>
      By signing up and using {Var.app.name} you automatically accept the <Link
        href=`${Var.app.website.url}/policies/terms-of-service/`
        target="_blank">Terms of Service</Link
      >, <Link
        href=`${Var.app.website.url}/policies/privacy-policy/`
        target="_blank">Privacy Policy</Link
      > and rest of the policies. If you have a question about any of these Terms,
      please <Link href=`${Var.app.website.url}/support/` target="_blank"
        >contact us</Link
      >
    </p>
  </Main>
</Site>
