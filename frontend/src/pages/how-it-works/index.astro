---
import Site from '../../layouts/Site.astro'
import Link from '../../components/elements/Link.astro'
import Main from '../../components/container/site/Main.astro'

import { Var } from '../../script/var'
import { currentPage, currentTopic, currentSubTopic } from '../../script/store'
currentPage.set('howItWorks')
currentTopic.set('')
currentSubTopic.set('')

const title = `How it Works | ${Var.app.name} `
const description =
  'Discover how to easily embed SenseFolks surveys on your website and collect valuable customer feedback'
---

<Site title={title} description={description}>
  <Main>
    <h2 class="spacing__vertical--100">How it Works</h2>
    <p class="footnote">STEP 1</p>
    <h3>Customize Your Survey Form</h3>
    <p>
      Tailor your survey with ease. Read our <Link
        href=`${Var.app.website.url}/docs/tutorials/styling-a-survey/`
        >customization guide</Link
      > for step-by-step instructions
    </p>
    <div class="separator spacing__vertical--200"></div>
    <p class="footnote">STEP 2</p>
    <h3>Distribute Your Survey Form</h3>
    <p>
      Send your survey directly to your audience or embed it on your website for
      continuous feedback. <Link
        href=`${Var.app.website.url}/docs/tutorials/embedding-a-survey/`
        >Learn how to embed forms</Link
      >
    </p>
    <div class="separator spacing__vertical--200"></div>
    <p class="footnote">STEP 3</p>
    <h3>Analyze Survey Data</h3>
    <p>
      Visualize responses and uncover actionable insights on the dashboard.
      Explore our <Link
        href=`${Var.app.website.url}/docs/tutorials/interpretation-basics/`
        >Interpretation Guide</Link
      > for best practices
    </p>
    <div class="separator spacing__vertical--200"></div>
    <p class="footnote">STEP 4</p>
    <h3>Make informed decisions ✨</h3>
    <p>
      Use data-driven insights to improve your product, pricing, and overall
      strategy
    </p>
  </Main>
</Site>
