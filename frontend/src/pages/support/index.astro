---
import Site from '../../layouts/Site.astro'
import Link from '../../components/elements/Link.astro'
import Main from '../../components/container/site/Main.astro'

import { Var } from '../../script/var'
import { currentPage, currentTopic, currentSubTopic } from '../../script/store'
currentPage.set('support')
currentTopic.set('')
currentSubTopic.set('')

const title = `Support | ${Var.app.name} `
const description =
  'Our dedicated support team is here to assist you with any questions or issues. Whether it’s setting up surveys, understanding insights, or navigating the dashboard, we’re ready to provide expert guidance to ensure you get the most out of SenseFolks'
---

<Site title={title} description={description}>
  <Main>
    <h2 class="spacing__vertical--100">Support</h2>
    <p>For assistance, feature requests or bug reports:</p>
    <Link href={`mailto:${Var.app.support.email}`}>{Var.app.support.email}</Link
    ><br />
    <Link href={Var.app.support.social.twitter} target="_blank"
      >{Var.app.support.social.twitter}</Link
    >
  </Main>
</Site>
