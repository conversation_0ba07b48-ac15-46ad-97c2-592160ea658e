---
import Site from '../layouts/Site.astro'
import Gallery from '../components/container/Gallery.astro'
import Link from '../components/elements/Link.astro'
import FaqItem from '../components/patterns/FaqItem.astro'


import { Var } from '../script/var'
import { currentPage, currentTopic, currentSubTopic } from '../script/store'
currentPage.set('home')
currentTopic.set('')
currentSubTopic.set('')

const title = `${Var.app.name} — ${Var.app.tagline}`
const description = Var.app.description
---

<Site title={title} description={description}>
    <section class="logo-section">
      <div class="logo-container">
        <img
          src="https://res.cloudinary.com/dyygc6dx2/image/upload/v1743453235/logotype_pxbcf5.svg"
          alt="SenseFolks Logo"
          class="sensefolks-logo"
          width="225"
          height="59"
        />
        <p class="footnote logo-footnote">
          Made by <Link href="https://projckt.com" theme="default" target="_blank">Projckt</Link>
        </p>
      </div>
    </section>
    <section class="hero-section spacing__vertical--900">
      <div class="spacing__vertical--900 hero-container">
        <h1>Read your customers' mind</h1>
        <div class="spacing__vertical--50"></div>
        <h2>Micro-surveys that help you <u>make epic product decisions</u></h2>
        <div class="dot-grid hero-dot-separator"></div>
        <!-- <div class="spacing__vertical--200"></div>
        <div class="hero__buttons spacing__vertical--100">
          <Link href="#surveys" variant="button" fill="outline" theme="default">
            <strong>Explore Surveys</strong>
          </Link>
          &nbsp;&nbsp;
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Get Started</strong>
          </Link>
        </div> -->
        <div class="spacing__vertical--400"></div>
        <Gallery>
          <div class="gallery__item">
            <img
              class="benefits__icons"
              src="./target.svg"
              width="56"
              height="56"
              alt="SenseFolks surveys gather feedback about a specific aspect of the product"
            />
            <h3><strong>Super Focused</strong></h3>
            <p>Each survey is designed to gather feedback about a specific aspect of the product</p>
          </div>
          <div class="gallery__item">
            <img
              class="benefits__icons"
              src="./wind.svg"
              width="56"
              height="56"
              alt="SenseFolks surveys use proven research methods for accurate insights"
            />
            <h3><strong>Trusted Insights</strong></h3>
            <p>Our surveys are based on proven research methods that yield accurate results</p>
          </div>
          <div class="gallery__item">
            <img
              class="benefits__icons"
              src="./bolt.svg"
              width="56"
              height="56"
              alt="No need for customisations. Embed SenseFolks surveys on your website"
            />
            <h3><strong>Quick Setup</strong></h3>
            <p>Launch surveys in minutes. Embed it on your website or share it as a link</p>
          </div>
        </Gallery>
      </div>
    </section>
    <section class="spacing__vertical--1200 dashboard-section full-width-section">
      <h1 style="color:#F3E5F5">Know what your customers <em>really want</em>. <span style="color: #E1BEE7">Join the waitist</span></h1>
      <div class="spacing__vertical--200"></div>
      <form class="waitlist-form" id="waitlist-form">
        <div class="form-group">
          <input type="email" placeholder="Enter your email" class="waitlist-input" id="waitlist-email" required>
          <button type="submit" class="waitlist-button">Join Waitlist</button>
        </div>
        <div class="error-message" id="waitlist-error"></div>
      </form>
      <div class="spacing__vertical--200"></div>
      <p style="color:#F3E5F5">* We will send you a notification when we launch.</p>
      <div class="spacing__vertical--400"></div>
    </section>
    <section id="surveys" class="spacing__vertical--1600">
      <article>
        <h1>The Surveys</h1>
        <div class="spacing__vertical--50"></div>
        <h2>Designed with intent and purpose</h2>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title survey__thumbnail__title--senseprice">SensePrice</h3>
            <div class="spacing__vertical--50"></div>
            <h3>Discover how much your customers are willing to pay</h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  Eliminate guesswork from pricing
                </p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  Confidently set the optimal price
                </p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Don't leave money on the table</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title survey__thumbnail__title--sensepriority">SensePriority</h3>
            <div class="spacing__vertical--50"></div>
            <h3>
              Prioritize features that drive customer satisfaction and retention
            </h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Identify high-impact features</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Enhance product-market fit</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Improve your ROI</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title survey__thumbnail__title--sensechoice">SenseChoice</h3>
            <div class="spacing__vertical--50"></div>
            <h3>
              Prioritize features that drive customer satisfaction and retention
            </h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Identify high-impact features</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Enhance product-market fit</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Improve your ROI</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title survey__thumbnail__title--sensepoll">SensePoll</h3>
            <div class="spacing__vertical--50"></div>
            <h3>Create highly flexible polls to capture opinions, preferences, and feedback</h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Use single or multi-choice questions</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Get clear & structured insights</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Identify trends & optimize your decisions</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title survey__thumbnail__title--sensequery">SenseQuery</h3>
            <div class="spacing__vertical--50"></div>
            <h3>Uncover unanswered questions and identify what your audience wants to know</h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Spot knowledge or content gaps</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Enhance clarity</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Eliminate confusion</p>
              </li>
            </ul>
          </div>
        </div>
      </article>
    </section>
    <!-- <section class="spacing__vertical--900">
      <article>
        <div class="cta__container">
          <h2 class="spacing__vertical--100">
            Get product feedback that matters
          </h2>
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Get Started</strong>
          </Link>
        </div>
      </article>
    </section> -->
    <section class="spacing__vertical--1600 dashboard-section full-width-section" style="margin-bottom: 0">
      <article>
        <h1 style="background: linear-gradient(to right, #80DEEA, #E0F7FA); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">The Dashboard</h1>
        <div class="spacing__vertical--50"></div>
        <h2>Everything in one place</h2>
        <div class="spacing__vertical--900">
          <div class="dashboard__thumbnail"></div>
          <div class="dashboard__benefit">
            <ul
              class="no-margin--left row row__justify--space-between row--wrap no-margin--bottom"
            >
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Create & organize surveys</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Get automated insights</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Make smart decisions</strong>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div class="spacing__vertical--900">
          <div class="dashboard__thumbnail"></div>
          <div class="dashboard__benefit">
            <ul
              class="no-margin--left row row__justify--space-between row--wrap no-margin--bottom"
            >
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Get insights by country</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Spot regional preferences</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Optimize regional strategies</strong>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div class="spacing__vertical--900">
          <div class="dashboard__thumbnail"></div>
          <div class="dashboard__benefit no-margin--bottom">
            <ul
              class="no-margin--left row row__justify--space-between row--wrap no-margin--bottom"
            >
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Define your personas</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Get insights by persona</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Optimize offerings for personas</strong>
                </p>
              </li>
            </ul>
          </div>
        </div>
      </article>
    </section>
    <section class="spacing__vertical--1600 full-width-section" style="background: #121212; text-align: center; color: white; margin-top: 0; padding: 8em 0;">
      <h1 style="color: white">Get deeper customer insights.<br/> <span style="color: rgba(255, 255, 255, 0.6)">Sign up for early access</span></h1>
      <div class="spacing__vertical--200"></div>
      <form class="early-access-form" id="early-access-form">
        <div class="form-group">
          <input type="email" placeholder="Enter your email" class="early-access-input" id="early-access-email" required>
          <button type="submit" class="early-access-button">Get Early Access</button>
        </div>
        <div class="error-message" id="early-access-error"></div>
      </form>
      <div class="spacing__vertical--200"></div>
      <p>* We will send you an early access invite when we launch</p>
    </section>
    <!-- <section class="spacing__vertical--900">
      <article>
        <div class="cta__container">
          <h2 class="spacing__vertical--100">
            Get automated analysis & insights
          </h2>
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Sign Up</strong>
          </Link>
        </div>
      </article>
    </section> -->
    <section class="spacing__vertical--600" id="faq">
      <article>
        <h1 class="spacing__vertical--100">FAQ</h1>
        <article class="content--narrow">

          <!-- Section 1: Introduction -->
          <h2>Introduction</h2>

          <FaqItem question="What is SenseFolks?">
            <p><strong>SenseFolks is a customer research platform</strong> built for people who make product decisions.</p>
            <div class="spacing__vertical--100"></div>
            <p><strong>It offers ready-made micro surveys</strong> focused on key product topics like pricing, feature prioritization, content clarity and more.</p>
            <div class="spacing__vertical--100"></div>
            <p>Unlike generic survey tools, <strong>SenseFolks not only collects responses but also analyzes them automatically</strong> to deliver precise insights in real time.</p>
            <div class="spacing__vertical--100"></div>
            <p><strong>So you can make better product decisions quickly</strong>, based on real customer feedback instead of guesswork.</p>
          </FaqItem>

          <div class="spacing__vertical--150"></div>
          
          <FaqItem question="How does SenseFolks work?">
            <ol>
              <li><strong>Choose a survey</strong> from our ready-made templates focused on pricing, feature priorities, or content clarity.</li>
              <div class="spacing__vertical--100"></div>
              <li><strong>Set it up</strong> by answering a few quick prompts. It takes less than two minutes.</li>
              <div class="spacing__vertical--100"></div>
              <li><strong>Embed it</strong> on your website in the right context or share it as a standalone link.</li>
              <div class="spacing__vertical--100"></div>
              <li><strong>Get insights automatically</strong> as responses come in. No manual analysis needed.</li>
            </ol>          
          </FaqItem>

          <div class="spacing__vertical--150"></div>
          
          <FaqItem question="Who is SenseFolks for?" id="who-is-sensefolks-for">
            <p><strong>SenseFolks is for anyone who makes decisions about a product or service.</strong></p>
            <div class="spacing__vertical--100"></div>
            <p>Whether you're a product manager, designer, marketer, founder, indie creator, or small business owner — if you care about what your customers think, SenseFolks is for you.</p>
            <div class="spacing__vertical--100"></div>
            <p>It works for individuals and teams alike, from early-stage startups to large enterprises, across any industry.</p>
            <div class="spacing__vertical--100"></div>
            <p><strong>If you need focused, actionable feedback to build better products, SenseFolks has you covered.</strong></p>          
          </FaqItem>
          
          <div class="spacing__vertical--150"></div>
          
          <FaqItem question="How is it different from other survey tools?">
            <p><strong>SenseFolks is purpose-built for the <a href="#who-is-sensefolks-for" onclick="document.getElementById('who-is-sensefolks-for').open = true" style="color: #3f51b5">product people</a></strong>. It is not just another generic form builder like Google Forms, Typeform, or SurveyMonkey.</p>
            <div class="spacing__vertical--100"></div>
            <p>It goes beyond collecting responses. <strong>SenseFolks automatically analyzes the data</strong> and gives you clear, real-time insights you can act on.</p>            
            <div class="spacing__vertical--100"></div>
            <p>No spreadsheets. No manual work. Just instant feedback that helps you make better product decisions.</p>
          </FaqItem>

          <div class="spacing__vertical--150"></div>

          <FaqItem question="Do I need technical knowledge to use this?">
            <p><strong>No, not at all.</strong></p>
            <div class="spacing__vertical--100"></div>
            <p><strong>SenseFolks surveys are based on proven research methods</strong>, but you don’t need to know any of the theory or technical details behind them. </p>
            <div class="spacing__vertical--100"></div>
            <p> SenseFolks is made for non-researchers who still need research-grade insights on pricing, customer opinions, and other key product topics.</p>
            <div class="spacing__vertical--100"></div>
            <p><strong>You get clear insights with zero effort.</strong> We handle the analysis and research logic for you.</p>
          </FaqItem>

          <div class="separator spacing__vertical--400"></div>

          <!-- Section 3: Key Features and Surveys -->
          <h2>About the Surveys</h2>
          <FaqItem question="What are micro-surveys?">
            <p>Micro-surveys are <strong>short, focused surveys</strong> with just one or a few questions about a specific topic.</p>
            <div class="spacing__vertical--100"></div>
            <p>They are designed to be <strong>quick and easy</strong> to complete, without adding friction to the user experience. So people are more likely to respond. </p>
            <div class="spacing__vertical--100"></div>
            <p>Micro-surveys are also <strong>highly contextual</strong> i.e. they are placed at the right moment or context, like:</p>
            <ul>
              <li>On a pricing page</li>
              <li>After a purchase</li>
              <li>During a feature tutorial or onboarding</li>
              <li>Inside the documentation</li>
              <li>When a user churns or downgrades</li>
            </ul>
            <div class="spacing__vertical--100"></div>
            <p>This ensures the feedback is <strong>highly relevant and actionable</strong>.</p>
            <div class="spacing__vertical--100"></div>
            <p>To summarise, micro-surveys are:</p>
            <ul>
              <li>Fast to complete</li>
              <li>Easy to set up</li>
              <li>Simple to analyze</li>
              <li>Great for <strong>getting clear focused insights</strong></li>
              <li>Does not overwhelm users</li>
            </ul>
          </FaqItem>
          <div class="spacing__vertical--150"></div>
          <FaqItem question="What types of micro-surveys are available in SenseFolks?">
            <p>SenseFolks offers a suite of ready-made micro-surveys, each designed for a specific purpose. They are:</p>
            <div class="spacing__vertical--100"></div>
            <p><strong>SensePrice</strong><br/> A pricing survey that reveals what customers are willing to pay. It removes guesswork and helps you price confidently without leaving money on the table.</p>
            <div class="spacing__vertical--100"></div>
            <p><strong>SensePriority</strong><br/> Identify which product features matter most. SensePriority uncovers high-impact, high-desire features that improve retention, satisfaction, and product-market fit.</p>
            <div class="spacing__vertical--100"></div>
            <p><strong>SensePoll</strong><br/> A lightweight and flexible polling tool for quick, structured feedback. Perfect for capturing simple opinions and preferences e.g. usefulness of content, features, or experiences.</p>
            <div class="spacing__vertical--100"></div>
            <p><strong>SenseQuery</strong><br/>   An open-ended, single-question format that lets users share exactly what’s unclear or missing. It helps you surface confusion, uncover knowledge gaps, and improve the clarity of your product, content, or documentation.
            </p>
          </FaqItem>
          <div class="spacing__vertical--150"></div>
          <FaqItem question="How are the surveys designed?">
            <p>
              Each SenseFolks survey is crafted with a specific purpose and backed by proven research frameworks. For example, SensePrice uses models like Van Westendorp or Gabor-Granger to identify optimal pricing, while SensePriority leverages techniques like Kano analysis to determine feature impact.
            </p>
          </FaqItem>
          <div class="spacing__vertical--150"></div>
          <FaqItem question="How are the surveys distributed?">
            <p>Surveys can be distributed in two ways:</p>
            <p><strong>Embedded:</strong> You can embed the survey directly into your website or product. This is done by copying a small code snippet (like a bit of HTML/JavaScript) provided by SenseFolks and pasting it into the relevant page. This ensures the survey is always up-to-date and accessible to users.</p>
            <p><strong>Shared Link:</strong> You can also get a shareable link for the survey and send it to users via email, social media, or any other communication channel. This is useful for standalone surveys or when you want to target specific groups.</p>
          </FaqItem>
          <div class="spacing__vertical--150"></div>
          <FaqItem question="How can I use SenseFolks surveys to make better product decisions?">
            <p>SenseFolks surveys remove guesswork by delivering concrete customer feedback on key issues. For example, you can place a survey on your pricing page to find out what price customers consider fair (using SensePrice), or on a features list to see which features users want most (using SensePriority). The platform then automatically analyzes responses and highlights the most important trends and preferences. This means you can confidently set the optimal price, decide which features to build next, refine your branding or content, and generally improve product-market fit based on real data rather than intuition.</p>
          </FaqItem>
          <div class="spacing__vertical--150"></div>
          <FaqItem question="Can I customize the look and feel of the surveys?">
            <p>SenseFolks is designed to be plug-and-play—no customization needed. The surveys are clean, minimal, and optimized for clarity. You can embed them directly onto your website or share a unique survey link. Advanced customization may be available in future versions.</p>          
          </FaqItem>
          <div class="spacing__vertical--150"></div>
          <FaqItem question="How do I embed a SenseFolks survey on my website?">
            <p>Embedding is simple. Once your survey is ready, you'll receive a small embed code snippet (just like embedding a YouTube video). Copy and paste it into your website's HTML where you want the survey to appear. That’s it. Alternatively, you can share it as a standalone link.</p>          
          </FaqItem>
          <div class="spacing__vertical--150"></div>


          <div class="separator spacing__vertical--400"></div>

          <!-- Section 4: Usability and Setup -->
          <h2>Usability and Setup</h2>

          <FaqItem question="Do I need any research or technical expertise to use SenseFolks?">
            <p>No expertise is required. SenseFolks is designed for people without formal research or coding backgrounds. The surveys and questions are already crafted using proven market research techniques, so you don't have to know how to write survey questions or analyze data. The platform's interface is intuitive, and step-by-step guidance is built in. You simply input your context or goals, and SenseFolks takes care of the methodology. Similarly, embedding the survey on your site requires only a simple copy-and-paste of a code snippet, not programming. In short, if you can manage a website and answer a few prompts, you can use SenseFolks effectively.</p>
          </FaqItem>
          <div class="spacing__vertical--150"></div>
          <FaqItem question="How quickly can I set up and launch a survey?">
            <p>SenseFolks is extremely fast to use. You can create a new survey in just a couple of minutes. After logging into the dashboard, you choose the survey type (for example, SensePrice or SensePoll), answer a few simple questions to customize it (like naming the survey or selecting options), and SenseFolks generates the survey instantly. Once the survey is created, you immediately get the embed code and share link. Because everything is pre-designed, there's no waiting, coding, or designing needed. You could literally go from zero to a live customer survey on your site in under five minutes.</p>
          </FaqItem>
          <div class="spacing__vertical--150"></div>
          <FaqItem question="How do I create and share a SenseFolks survey on my website?">
            <p>Creating a survey with SenseFolks is quick and easy. In the SenseFolks dashboard, you simply select the survey type you want and answer a few setup questions. The platform automatically generates the questions for you, based on proven research methods. Once your survey is ready, SenseFolks provides a small embed code snippet (like a bit of HTML/JavaScript) that you can copy into your website or product. You can also get a shareable link to send the survey directly to users via email or social media. This means <em>no coding or design skills are required</em> — you just paste the provided code where you want the survey to appear, or share the link with your audience.</p>
          </FaqItem>
          <div class="spacing__vertical--150"></div>
          <FaqItem question="How is SenseFolks different from other survey tools like Typeform or Google Forms?">
            <p>Unlike generic form tools, SenseFolks is built specifically for product teams looking for actionable customer insights. Here are a few key differences:</p>
            <p>- <strong>Pre-built, research-backed surveys:</strong> SenseFolks provides ready-made survey templates for common use cases (pricing, feature prioritization, polls, etc.) that are based on established research methods. With Typeform or Google Forms, you typically have to design and customize every question yourself.</p>
            <p>- <strong>Focus and context:</strong> SenseFolks surveys are micro-surveys designed to be placed in context (e.g., on a pricing page or next to a specific feature). This ensures the feedback is highly relevant. Generic tools don't guide you on what to ask or where to place the survey.</p>
            <p>- <strong>Automatic analysis:</strong> SenseFolks automatically processes and analyzes survey responses for you, highlighting trends and key insights. Traditional survey tools give you raw data, and you'd have to analyze it manually.</p>
            <p>- <strong>Ease of use:</strong> With SenseFolks you can launch a survey in minutes with minimal effort. There's no need for form-building, custom coding, or data wrangling. Typeform and Google Forms can also be quick to set up, but they lack the specialized focus and built-in insights that SenseFolks offers.</p>
          </FaqItem>

          <div class="separator spacing__vertical--400"></div>

          <!-- Section 5: Dashboard and Insights -->
          <h2>Dashboard and Insights</h2>

          <FaqItem question="What is the SenseFolks dashboard?">
            <p>The dashboard is your command center. From here, you can create, manage, and track all your surveys. It provides:</p>
            <ul>
              <li>Automated insights</li>
              <li>Breakdowns by country or region</li>
              <li>Persona-based analysis</li>
              <li>Organization tools to group and compare surveys</li>
            </ul>
          </FaqItem>

          <div class="spacing__vertical--150"></div>

          <FaqItem question="What kind of insights can I expect?">
            <p>SenseFolks gives you targeted, structured insights that help you make specific product decisions. For example, you’ll know:</p>
            <ul>
              <li>What price point works best (SensePrice)</li>
              <li>Which features to build next (SensePriority, SenseChoice)</li>
              <li>What your users are confused about (SenseQuery)</li>
              <li>How preferences differ across personas or regions (Dashboard filters)</li>
            </ul>
          </FaqItem>

          <div class="spacing__vertical--150"></div>

          <FaqItem question="What kind of insights can I expect?">
            <p>SenseFolks gives you targeted, structured insights that help you make specific product decisions. For example, you’ll know:</p>
            <ul>
              <li>What price point works best (SensePrice)</li>
              <li>Which features to build next (SensePriority, SenseChoice)</li>
              <li>What your users are confused about (SenseQuery)</li>
              <li>How preferences differ across personas or regions (Dashboard filters)</li>
            </ul>
          </FaqItem>

          <div class="spacing__vertical--150"></div>

          <FaqItem question="Can I analyze survey results by region or persona?">
            <p>Yes. SenseFolks helps you spot regional preferences and optimize your strategy accordingly. You can also define user personas (e.g., new users vs. power users) and filter insights based on those segments.</p>
          </FaqItem>

          <div class="spacing__vertical--150"></div>

          <FaqItem question="What is the SenseFolks Dashboard and what insights does it provide?">
            <p>The SenseFolks Dashboard is your central hub for managing surveys and viewing results. In the dashboard you can create new surveys, organize all your existing surveys in one place, and monitor response rates. More importantly, the dashboard automatically analyzes incoming feedback and turns it into easy-to-read insights. For example, after a survey runs, you'll see summary statistics and charts highlighting the most common answers and trends.</p>
            <p>The dashboard also lets you slice the data by different segments: you can filter responses by country to spot regional preferences, or by customer persona to see how different user types answered. This makes it easy to define and compare audience personas (e.g., "frequent buyers" vs. "trial users") and tailor your strategy. Overall, the dashboard helps you make smart, data-driven decisions without having to export or crunch data manually.</p>
          </FaqItem>

          <div class="separator spacing__vertical--400"></div>

          <!-- Section 6: Access and Availability -->
          <h2>Access and Availability</h2>

          <FaqItem question="Is SenseFolks available to use now?">
            <p>SenseFolks is currently preparing for launch, and you can get early access by joining the waitlist. To do this, simply enter your email address in the "Join Waitlist" or "Get Early Access" form on our website. We'll notify you via email when we launch and send you an invite to start using SenseFolks. Early access means you'll be among the first to try the platform and provide feedback. There's no commitment or fee to join the waitlist — we'll invite you to explore SenseFolks for free during the beta or initial launch phase.</p>
          </FaqItem>

          <div class="spacing__vertical--150"></div>

          <FaqItem question="How do I get started with SenseFolks or join the waitlist?">
            <p>SenseFolks is currently in pre-launch. You can join the waitlist by entering your email on our homepage. We’ll notify you as soon as early access opens up.</p>          </FaqItem>

          <div class="spacing__vertical--150"></div>
          <FaqItem question="When will SenseFolks be available?">
            <p>SenseFolks is currently in pre-launch and we're working hard to roll out the final version. We don't have an exact public launch date yet, but by joining the waitlist you'll get notified as soon as we're ready. Invitations for early access will be sent out when we launch the platform. In the meantime, you can explore more information on our website, sign up for updates, and follow us on social media for announcements. We appreciate your patience and can't wait to help you gain deeper customer insights soon!</p>
          </FaqItem>

          <div class="separator spacing__vertical--400"></div>

          <!-- Section 7: Company Background -->
          <h2>About Us</h2>

          <FaqItem question="Who is behind SenseFolks?">
            <p>SenseFolks is made by <strong>Projckt</strong>, a product studio focused on crafting tools that help people make better decisions through structured thinking and feedback-driven design.</p>
          </FaqItem>

        </article>
      </article>
    </section>

    <!-- Final Waitlist Section -->
    <section style="margin: 12em 0;" class="full-width-section final-waitlist-section dot-grid">
      <article>
        <h1 style="color: black;">Eliminate guesswork from your product decisions</h1>
        <div class="spacing__vertical--200"></div>
        <form class="final-waitlist-form" id="final-waitlist-form">
          <div class="form-group">
            <input type="email" placeholder="Enter your email" class="final-waitlist-input" id="final-waitlist-email" required>
            <button type="submit" class="final-waitlist-button">Get Early Access</button>
          </div>
          <div class="error-message" id="final-waitlist-error"></div>
        </form>
        <div class="spacing__vertical--200"></div>
      </article>
    </section>
    <section>
      <article>
        <div class="copyright-section">
          <p class="footnote">© {new Date().getFullYear()}&nbsp;{Var.app.name}. All rights reserved.</p>
          <p class="footnote">Made by <Link href="https://projckt.com" theme="default" target="_blank">Projckt</Link></p>
        </div>
      </article>
    </section>
    <!-- <section class="spacing__vertical--600">
      <article>
        <div class="cta__container">
          <h2 class="spacing__vertical--100">
            Drastically improve your product
          </h2>
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Get Started</strong>
          </Link>
        </div>
      </article>
    </section> -->
</Site>

<style>
  h1 {
    font-weight: 400;
    font-size: 3.5em;
  }

  body {
    width: 100%;
    max-width: none;
  }

  article {
    width: 870px;
    margin: 0 auto;
  }

  section {
    text-align: center;
    max-width: 1024px;
    margin: 0 auto;
  }

  .full-width-section {
    max-width: none;
    width: 100%;
  }

  .dashboard__benefit {
    text-align: left;
  }

  .dashboard__benefit .row {
    align-items: center;
  }

  .dashboard-section {
    background: linear-gradient(135deg, var(--color__indigo--900) 0%, var(--color__indigo--700) 100%);
    padding: calc(var(--padding) * 4 + 5em) calc(var(--padding) * 4) calc(var(--padding) * 4) calc(var(--padding) * 4);
    position: relative;
  }

  .dashboard-section h1 {
    color: var(--color__white);
  }

  .dashboard-section h2 {
    color: var(--color__indigo--50);
  }

  .dashboard-section p {
    color: var(--color__white);
  }



  .dot-separator {
    height: 100px;
    width: 100%;
  }

  .hero-dot-separator {
    width: 100%;
    aspect-ratio: 16 / 9;
    margin-top: 3em;
  }

  .spacing__vertical--1600 {
    margin: 16em 0;
  }

  .copyright-section {
    text-align: center;
    margin-bottom: 2em;
  }

  .copyright-section p {
    margin: 0.5em 0;
    color: var(--color__grey--500);
  }

  .logo-section {
    display: flex;
    justify-content: center;
    align-items: center;
    /* padding: 4em 0 2em 0;
     */
     margin-top: 4em;
  }

  .logo-container {
    text-align: center;
  }

  .sensefolks-logo {
    max-width: 100%;
    height: auto;
  }

  .logo-footnote {
    margin-bottom: 0;
    text-align: right;
  }

  .hero-section {
    margin-top: 3em;
  }

  .content--narrow {
    width: 560px;
    text-align: left;
    margin: 0 auto;
  }

  .gallery__item {
    width: 310px;
    text-align: center;
    background: var(--color__white);
    padding: calc(var(--padding) * 2) calc(var(--padding) * 1.5);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(63, 81, 181, 0.15);
    border: 1px solid rgba(63, 81, 181, 0.1);
  }

  .cta__container {
    color: var(--color__grey--800);
    border: 1px solid var(--color__grey--800);
    background: var(--color__grey--50);
    padding: var(--padding) var(--padding) calc(3 * var(--padding)) var(--padding);
    border-radius: calc(var(--border-radius) * 2);
  }

  .cta__container h2 {
    color: var(--color__grey--700);
  }

  .survey__benefits .row {
    align-items: center;
  }

  .survey__preview__bg {
    height: 400px;
    width: 50%;

  }

  .survey__thumbnail__description {
    width: 45%;
    text-align: left;
  }

  .survey__thumbnail__description h2 {
    margin-bottom: 0.5em;
  }

  .survey__thumbnail__title {
    display: inline-block;
    border: 1px solid var(--color__indigo--300);
    padding: 0.25em 1em;
    border-radius: var(--border-radius);
    margin-bottom: 0.5em;
  }

  .survey__thumbnail__title--senseprice {
    border-color: #6a1b9a;
    color: #6a1b9a;
    background: #f3e5f5;
  }

  .survey__thumbnail__title--sensechoice {
    border-color: #1565c0;
    color: #1565c0;
    background: #e3f2fd;
  }

  .survey__thumbnail__title--sensepoll {
    border-color: #303f9f;
    color: #303f9f;
    background: #e8eaf6;
  }

  .survey__thumbnail__title--sensequery {
    border-color: #006064;
    color: #006064;
    background: #e0f7f9;
  }

  .survey__thumbnail__title--sensepriority {
    border-color: #00695c;
    color: #00695c;
    background: #e0f2f1;
  }

  .black-cta-section {
    background: #000000;
    padding: 4em 0;
    margin: 0 calc(-50vw + 50%);
    width: 100vw;
  }

  .black-cta-section .cta__container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 var(--padding);
    text-align: center;
  }

  /* Final Waitlist Section */
  .final-waitlist-section {
    padding: 4em 0;
    width: 100vw;
    text-align: center;
  }

  .final-waitlist-section article {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 var(--padding);
  }

  /* Waitlist Form Styles */
  .waitlist-form, .early-access-form, .final-waitlist-form {
    max-width: calc(var(--padding) * 31.25); /* 500px equivalent */
    margin: 0 auto;
  }

  .form-group {
    display: flex;
    align-items: stretch;
  }

  .waitlist-input {
    flex: 1;
    padding: var(--padding) calc(var(--padding) * 1.25);
    border: 1px solid white;
    border-radius: calc(var(--border-radius) * 1.6) 0 0 calc(var(--border-radius) * 1.6);
    border-right: none;
    font-size: calc(var(--font-size__body) * 1.06);
    outline: none;
    background: none;
    color: #F3E5F5;
  }

  .early-access-input {
    flex: 1;
    padding: var(--padding) calc(var(--padding) * 1.25);
    border: 1px solid white;
    border-radius: calc(var(--border-radius) * 1.6) 0 0 calc(var(--border-radius) * 1.6);
    border-right: none;
    font-size: calc(var(--font-size__body) * 1.06);
    outline: none;
    background: none;
    color: white;
  }

  .final-waitlist-input {
    flex: 1;
    padding: var(--padding) calc(var(--padding) * 1.25);
    border: 1px solid var(--color__grey--400);
    border-radius: calc(var(--border-radius) * 1.6) 0 0 calc(var(--border-radius) * 1.6);
    border-right: none;
    font-size: calc(var(--font-size__body) * 1.06);
    outline: none;
    background: white;
    color: black;
  }

  .waitlist-input::placeholder {
    color: rgba(255, 255, 255, 0.8);
  }

  .early-access-input::placeholder {
    color: rgba(255, 255, 255, 0.8);
  }

  .final-waitlist-input::placeholder {
    color: rgba(0, 0, 0, 0.6);
  }

  .waitlist-button {
    padding: var(--padding) calc(var(--padding) * 2);
    border: 1px solid white;
    border-radius: 0 calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6) 0;
    background: white;
    color: var(--color__indigo--700);
    font-size: calc(var(--font-size__body) * 1.06);
    font-weight: 600;
    cursor: pointer;
    white-space: nowrap;
  }

  .early-access-button {
    padding: var(--padding) calc(var(--padding) * 2);
    border: 1px solid white;
    border-radius: 0 calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6) 0;
    background: white;
    color: #121212;
    font-size: calc(var(--font-size__body) * 1.06);
    font-weight: 600;
    cursor: pointer;
    white-space: nowrap;
  }

  .final-waitlist-button {
    padding: var(--padding) calc(var(--padding) * 2);
    border: 1px solid var(--color__grey--400);
    border-radius: 0 calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6) 0;
    background: var(--color__indigo--500);
    color: white;
    font-size: calc(var(--font-size__body) * 1.06);
    font-weight: 600;
    cursor: pointer;
    white-space: nowrap;
    transition: background-color 0.25s;
  }

  .final-waitlist-button:hover {
    background: var(--color__indigo--700);
  }

  .error-message {
    color: var(--color__red--100);
    font-size: calc(var(--font-size__body) * 0.82);
    margin-top: var(--padding);
    text-align: center;
    display: none;
  }

  .spinner {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: var(--padding);
    height: var(--padding);
    animation: spin 1s linear infinite;
    display: inline-block;
    margin: 0 auto;
  }

  .spinner.waitlist-spinner {
    border-top: 2px solid var(--color__indigo--700);
  }

  .spinner.early-access-spinner {
    border-top: 2px solid #121212;
  }

  .spinner.final-waitlist-spinner {
    border-top: 2px solid white;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .waitlist-button.loading,
  .early-access-button.loading,
  .final-waitlist-button.loading {
    pointer-events: none;
    opacity: 0.8;
  }

  .early-access-input {
    border-color: white;
    background: none;
    color: white;
  }

  .early-access-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
  }

  .early-access-button {
    border-color: white;
    background: white;
    color: #121212;
  }

  .dashboard__thumbnail {
    display: flex;
    justify-content: space-around;
    align-items: center;
    border: 1px solid var(--color__indigo--800);
    height: calc(var(--padding) * 31.25); /* 500px equivalent */
    border-radius: var(--border-radius);
    width: 100%;
  }

  .masonry {
    display: flex; flex-wrap: wrap; gap: calc(var(--padding) * 2.5); justify-content: space-around;
  }

  .masonry-row {
    display: flex;
    align-items: center;
  }

  @media only screen and (max-width: 480px) {
    body {
      width: 90%;
      margin: 0 auto;
    }

    article {
      width: 100%;
      text-align: left;
    }

    section {
      text-align: left;
    }

    .gallery__item {
      width: 100%;
      text-align: left;
      padding: calc(var(--padding) * 1.5);
      margin-bottom: calc(var(--padding) * 1.5);
    }

    .hero__buttons {
      display: flex;
    }

    .logo-section {
      justify-content: flex-start;
      /*
      padding: 2em 0 1em 0; */
      margin-top: 2em;
    }

    .logo-container {
      text-align: left;
    }

    .sensefolks-logo {
      width: 188px;
      max-width: 90vw;
    }

    .logo-footnote {
      text-align: right;
    }

    /* Mobile form styles */
    .form-group {
      flex-direction: column;
    }

    .waitlist-input {
      border-radius: calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6) 0 0;
      border-right: 1px solid white;
      border-bottom: none;
    }

    .early-access-input {
      border-radius: calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6) 0 0;
      border-right: 1px solid white;
      border-bottom: none;
    }

    .final-waitlist-input {
      border-radius: calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6) 0 0;
      border-right: 1px solid var(--color__grey--400);
      border-bottom: none;
    }

    .waitlist-button {
      border-radius: 0 0 calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6);
      border-top: none;
    }

    .early-access-button {
      border-radius: 0 0 calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6);
      border-top: none;
    }

    .final-waitlist-button {
      border-radius: 0 0 calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6);
      border-top: none;
    }

    .benefits__row {
      display: block;
    }

    .masonry {
      display: block;
    }

    .masonry-row {
      align-items: baseline;
      margin-bottom: calc(var(--padding) * 2);
    }

    .dashboard__benefit .row {
      align-items: baseline;
    }

    .content--narrow {
      width: 100%;
    }

    .survey__benefits .row,
    .dashboard__benefit .row {
      display: flex;
      align-items: baseline;
    }

    .survey__preview__bg {
      width: 100%;
    }

    .survey__thumbnail__description {
      width: 100%;
    }

    .cta__container {
      padding: calc(var(--padding) * 1.5);
    }

    .material-symbols-outlined {
      z-index: 9;
    }

    .dashboard-section {
      padding: calc(var(--padding) * 2 + 2em) calc(var(--padding) * 2) calc(var(--padding) * 2) calc(var(--padding) * 2);
    }
  }

  /* FAQ Styles */
  #faq h2 {
    margin-top: 2.5em;
    margin-bottom: 1em;
  }

  /* FAQ internal links */
  #faq a {
    color: inherit;
    text-decoration: underline;
    text-decoration-color: var(--color__indigo--300);
    transition: all 0.25s;
  }

  #faq a:hover {
    text-decoration-color: var(--color__indigo--500);
    color: var(--color__indigo--700);
  }
</style>

<script>
  // Email validation function
  function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Show error message
  function showError(errorElementId: string, message: string): void {
    const errorElement = document.getElementById(errorElementId);
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.style.display = 'block';
    }
  }

  // Hide error message
  function hideError(errorElementId: string): void {
    const errorElement = document.getElementById(errorElementId);
    if (errorElement) {
      errorElement.style.display = 'none';
    }
  }

  // Validate form
  function validateForm(emailInputId: string, errorElementId: string): boolean {
    const emailInput = document.getElementById(emailInputId) as HTMLInputElement;
    if (!emailInput) return false;

    const email = emailInput.value.trim();

    hideError(errorElementId);

    if (!email) {
      showError(errorElementId, 'Please enter your email address');
      return false;
    }

    if (!isValidEmail(email)) {
      showError(errorElementId, 'Please enter a valid email address');
      return false;
    }

    return true;
  }

  // Show spinner in button
  function showSpinner(buttonElement: HTMLButtonElement | null, spinnerClass: string): void {
    if (buttonElement) {
      buttonElement.innerHTML = '<div class="spinner ' + spinnerClass + '"></div>';
      buttonElement.classList.add('loading');
    }
  }

  // Hide spinner and restore button text
  function hideSpinner(buttonElement: HTMLButtonElement | null, originalText: string): void {
    if (buttonElement) {
      buttonElement.innerHTML = originalText;
      buttonElement.classList.remove('loading');
    }
  }

  // Submit form to API
  async function submitToWaitlist(email: string, location: string): Promise<{success: boolean, message: string}> {
    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    const apiUrl = isDevelopment
      ? 'http://localhost:4444/v1/waitlist'
      : 'https://api.sensefolks.com/v1/waitlist';

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: email,
        meta: {
          source: 'homepage',
          location: location
        }
      })
    });

    return await response.json();
  }

  // Show success message and hide form
  function showSuccess(formElement: HTMLFormElement, message: string): void {
    if (formElement) {
      formElement.style.display = 'none';
      const successDiv = document.createElement('div');
      successDiv.style.color = '#81c784'; // Green text color
      successDiv.style.fontSize = 'calc(var(--font-size__body) * 0.94)';
      successDiv.style.marginTop = 'var(--padding)';
      successDiv.style.textAlign = 'center';
      successDiv.textContent = message;
      formElement.parentNode?.insertBefore(successDiv, formElement.nextSibling);
    }
  }

  // Add event listeners when DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    // Waitlist form validation
    const waitlistForm = document.getElementById('waitlist-form') as HTMLFormElement;
    if (waitlistForm) {
      waitlistForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const submitButton = waitlistForm.querySelector('.waitlist-button') as HTMLButtonElement;
        const originalText = submitButton ? submitButton.innerHTML : 'Join Waitlist';
        const emailInput = document.getElementById('waitlist-email') as HTMLInputElement;

        if (validateForm('waitlist-email', 'waitlist-error')) {
          // Show spinner
          showSpinner(submitButton, 'waitlist-spinner');

          try {
            const result = await submitToWaitlist(emailInput.value.trim(), 'below-hero');
            hideSpinner(submitButton, originalText);

            if (result.success) {
              showSuccess(waitlistForm, 'Thanks for your interest! We will get in touch soon');
            } else {
              showError('waitlist-error', result.message || 'Something went wrong. Please try again.');
            }
          } catch (error) {
            hideSpinner(submitButton, originalText);
            showError('waitlist-error', 'Network error. Please try again.');
          }
        }
      });
    }

    // Early access form validation
    const earlyAccessForm = document.getElementById('early-access-form') as HTMLFormElement;
    if (earlyAccessForm) {
      earlyAccessForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const submitButton = earlyAccessForm.querySelector('.early-access-button') as HTMLButtonElement;
        const originalText = submitButton ? submitButton.innerHTML : 'Get Early Access';
        const emailInput = document.getElementById('early-access-email') as HTMLInputElement;

        if (validateForm('early-access-email', 'early-access-error')) {
          // Show spinner
          showSpinner(submitButton, 'early-access-spinner');

          try {
            const result = await submitToWaitlist(emailInput.value.trim(), 'below-dashboard');
            hideSpinner(submitButton, originalText);

            if (result.success) {
              showSuccess(earlyAccessForm, 'Thanks for your interest! We will get in touch soon');
            } else {
              showError('early-access-error', result.message || 'Something went wrong. Please try again.');
            }
          } catch (error) {
            hideSpinner(submitButton, originalText);
            showError('early-access-error', 'Network error. Please try again.');
          }
        }
      });
    }

    // Final waitlist form validation
    const finalWaitlistForm = document.getElementById('final-waitlist-form') as HTMLFormElement;
    if (finalWaitlistForm) {
      finalWaitlistForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const submitButton = finalWaitlistForm.querySelector('.final-waitlist-button') as HTMLButtonElement;
        const originalText = submitButton ? submitButton.innerHTML : 'Get Early Access';
        const emailInput = document.getElementById('final-waitlist-email') as HTMLInputElement;

        if (validateForm('final-waitlist-email', 'final-waitlist-error')) {
          // Show spinner
          showSpinner(submitButton, 'final-waitlist-spinner');

          try {
            const result = await submitToWaitlist(emailInput.value.trim(), 'above-footer');
            hideSpinner(submitButton, originalText);

            if (result.success) {
              showSuccess(finalWaitlistForm, 'Thanks for your interest! We will get in touch soon');
            } else {
              showError('final-waitlist-error', result.message || 'Something went wrong. Please try again.');
            }
          } catch (error) {
            hideSpinner(submitButton, originalText);
            showError('final-waitlist-error', 'Network error. Please try again.');
          }
        }
      });
    }
  });
</script>
