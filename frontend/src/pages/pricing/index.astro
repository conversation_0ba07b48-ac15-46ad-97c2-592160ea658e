---
import Site from '../../layouts/Site.astro'
import Link from '../../components/elements/Link.astro'
import Main from '../../components/container/site/Main.astro'

import { Var } from '../../script/var'
import { currentPage, currentTopic, currentSubTopic } from '../../script/store'
currentPage.set('pricing')
currentTopic.set('')
currentSubTopic.set('')

const title = `Pricing | ${Var.app.name} `
const description =
  "Explore our flexible pricing options designed to meet your business needs. Whether you're a startup, an established company or an individual author, our plans offer access to ready-made surveys, automated insights, and continuous feedback to help you make smarter, data-driven decisions"
---

<Site title={title} description={description}>
  <Main variant="wide">
    <div
      class="spacing__vertical--200"
      style="display: flex; justify-content: space-around;"
    >
      <div>
        <p>Choose Billing Period</p>
        <div class="radio-container spacing__vertical--50">
          <div class="input-label__container">
            <input
              id="monthly-billing"
              type="radio"
              class="radio-input"
              name="billing"
              value="monthly"
              checked="true"
            />
            <label class="radio-label" for="monthly-billing"> Monthly </label>
          </div>
          <div class="input-label__container">
            <input
              id="yearly-billing"
              type="radio"
              class="radio-input"
              name="billing"
              value="yearly"
            />
            <label class="radio-label" for="yearly-billing"> Yearly </label>
          </div>
        </div>
      </div>
    </div>
    <div class="price__tier__row">
      <div class="price__tier__container">
        <h3 class="text--light">BASIC</h3>
        <h1 id="basic-pricing"></h1>
        <h3 class="billing-period"></h3>
        <div class="separator spacing__vertical--100"></div>
        <p id="tier-cap--basic" class="no-margin--bottom"></p>
        <div class="separator spacing__vertical--100"></div>
        <ul class="no-margin--left">
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">Unlimited survey links</p>
          </li>
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">Unlimited embeds</p>
          </li>
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">Email Support</p>
          </li>
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">Export survey data</p>
          </li>
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">Includes 7-day trial</p>
          </li>
        </ul>
        <div class="separator spacing__vertical--100"></div>
        <p class="footnote spacing__vertical--200">
          ** Credit card is not required to initiate the trial period
        </p>
        <Link
          href=`${Var.app.url}/signup/`
          variant="button"
          fill="solid"
          theme="default"
          size="wide"><strong>Start Basic Trial</strong></Link
        >
      </div>
      <div class="display-on-mobile">
        <div class="spacing__vertical--400"></div>
      </div>
      <div class="price__tier__container price__tier__container--highlighted">
        <h3>PROFESSIONAL</h3>
        <h1 id="pro-pricing"></h1>
        <h3 class="billing-period"></h3>
        <div class="separator spacing__vertical--100"></div>
        <p id="tier-cap--pro" class="no-margin--bottom"></p>
        <div class="separator spacing__vertical--100"></div>
        <ul class="no-margin--left">
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check--light.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">Unlimited survey links</p>
          </li>
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check--light.svg"
              width="24"
              height="32"
              alt="green check"
            />

            <p class="no-margin--bottom">Unlimited embeds</p>
          </li>
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check--light.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">Email Support</p>
          </li>
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check--light.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">Export survey data</p>
          </li>
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check--light.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">Includes 7-day trial</p>
          </li>
        </ul>
        <div class="separator spacing__vertical--100"></div>
        <p class="footnote spacing__vertical--200">
          ** Credit card is not required to initiate the trial period
        </p>
        <Link
          href=`${Var.app.url}/signup/`
          variant="button"
          fill="solid"
          theme="white"
          size="wide"><strong>Start Professional Trial</strong></Link
        >
      </div>
      <div class="display-on-mobile">
        <div class="spacing__vertical--400"></div>
      </div>
      <div class="price__tier__container">
        <h3 class="text--light">ENTERPRISE</h3>
        <h1 class="no-margin--bottom">Custom pricing</h1>
        <div class="separator spacing__vertical--100"></div>
        <p>
          No. of survey responses
          <strong>as per requirements</strong>
        </p>
        <div class="separator spacing__vertical--100"></div>
        <ul class="no-margin--left spacing__vertical--100">
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">
              Everything in <strong>Professional</strong>
            </p>
          </li>
          <li class="row row__benefits">
            <img
              class="material-symbols-outlined"
              src="/check.svg"
              width="24"
              height="32"
              alt="green check"
            />
            <p class="no-margin--bottom">Telephonic Support</p>
          </li>
        </ul>
        <Link
          href=`${Var.app.website.url}/pricing/schedule-call/`
          variant="button"
          fill="solid"
          theme="default"
          size="wide"><strong>Schedule a Call</strong></Link
        >
      </div>
    </div>
  </Main>
</Site>

<style>
  section {
    max-width: 100%;
  }

  label:hover {
    cursor: pointer;
  }

  .billing-controls__container {
    justify-content: space-around;
    /* border: var(--border);
    border-radius: var(--border-radius);
    padding: calc(1.5 * var(--padding));
    background: white; */
  }

  .billing-controls__buttons {
    display: flex;
  }

  .price__tier__container {
    width: 300px;
    display: block;
    text-align: left;
    border: var(--border);
    border-radius: var(--border-radius);
    padding: calc(1.5 * var(--padding));
    background: white;
  }

  .price__tier__container--highlighted {
    width: 320px;
    padding: calc(1.5 * var(--padding));
    border-radius: calc(var(--border-radius) * 2);
    background:
      radial-gradient(circle at 25% 30%, rgba(0, 150, 120, 0.5), transparent),
      radial-gradient(circle at 75% 20%, rgba(160, 50, 100, 0.5), transparent),
      radial-gradient(circle at 50% 80%, rgba(50, 100, 200, 0.6), transparent),
      #02030a;
  }

  .price__tier__container--highlighted h3 {
    color: var(--color__grey--300);
  }

  .price__tier__container--highlighted,
  .price__tier__container--highlighted h1,
  .price__tier__container--highlighted .footnote,
  .price__tier__container--highlighted .billing-period {
    color: var(--color__grey--50);
  }

  .price__tier__container--highlighted .separator {
    border-bottom: 1px solid var(--color__grey--500);
  }

  .price__tier__row {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
  }

  .align-top {
    align-items: baseline;
  }

  .radio-container {
    display: flex;
    gap: 1em;
    font-family: sans-serif;
  }

  .radio-label {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: calc(var(--padding) * 0.5) var(--padding);
    border: var(--border__input);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.25s ease-in-out;
    position: relative;
    padding-left: calc(
      var(--padding) * 2.5
    ); /* Increased padding to accommodate the gap */
  }

  .radio-label:hover {
    cursor: pointer;
    color: var(--color__grey--800);
    border: 1px solid var(--color__grey--800);
  }

  .radio-label::before {
    content: '';
    position: absolute;
    left: var(--padding);
    top: 50%;
    transform: translateY(-50%);
    width: 16px; /* Smaller circle */
    height: 16px; /* Smaller circle */
    border: 1px solid var(--color__grey--500);
    border-radius: 50%;
    background-color: white;
    transition: all 0.25s ease-in-out;
  }

  .radio-label::after {
    content: '';
    position: absolute;
    /* Center the dot within the circle both horizontally and vertically */
    left: calc(
      var(--padding) + 7px - 2px
    ); /* Calculate center of outer circle then offset by half inner circle width */
    top: 50%;
    transform: translateY(-50%);
    width: 6px; /* Smaller inner circle */
    height: 6px; /* Smaller inner circle */
    border-radius: 50%;
    background-color: var(--color__grey--800);
    opacity: 0;
    transition: all 0.25s ease-in-out;
  }

  /* Add a gap between the circle and the content */
  .radio-label slot,
  .radio-label ::slotted(*) {
    margin-left: 1em;
  }

  .radio-input {
    display: none;
  }

  .radio-input:checked + .radio-label::after {
    opacity: 1;
  }

  .radio-input:checked + .radio-label {
    color: var(--color__grey--800);
    border: 1px solid var(--color__grey--800);
    background: var(--color__grey--100);
  }

  .radio-input:checked + .radio-label::before {
    border-color: var(--color__grey--800);
  }

  .input-label__container {
    position: relative;
  }

  @media only screen and (max-width: 480px) {
    .billing-duration-container {
      display: block;
    }

    .billing-duration-tab {
      display: flex;
    }

    .price__tier__row {
      display: block;
    }

    .price__tier__container {
      width: 100%;
    }

    .material-symbols-outlined {
      position: relative;
      top: 10px;
      z-index: 9;
    }

    .row__benefits {
      display: flex;
      align-items: baseline;
    }
  }
</style>

<script>
  import { currentBilling } from '../../script/store'

  const billingOptions = document.querySelectorAll('input[name="billing"]')
  billingOptions.forEach((radio) => {
    radio.addEventListener('click', (event) => {
      const selectedRadio = event.target as HTMLInputElement
      currentBilling.set(selectedRadio.value)
    })
  })

  const basicPricingEl = document.getElementById('basic-pricing')!
  const proPricingEl = document.getElementById('pro-pricing')!
  const billingPeriodElements = document.querySelectorAll('.billing-period')
  const tierCapBasicEl = document.getElementById('tier-cap--basic')!
  const tierCapProEl = document.getElementById('tier-cap--pro')!

  let basicCap: string = ''
  let proCap: string = ''

  currentBilling.subscribe((newBillingPeriod) => {
    let billingPeriodLabel = ''

    if (newBillingPeriod === 'monthly') {
      billingPeriodLabel = 'Per Month'
      basicPricingEl.textContent = '$29'
      proPricingEl.textContent = '$99'
      basicCap = '1,000'
      proCap = '12,000'
    } else if (newBillingPeriod === 'yearly') {
      billingPeriodLabel = 'Per Year'
      basicPricingEl.textContent = '$299'
      proPricingEl.textContent = '$999'
      basicCap = '12,000'
      proCap = '150,000'
    }

    tierCapBasicEl.innerHTML = `Upto <strong>${basicCap} survey responses</strong> ${billingPeriodLabel.toLowerCase()}`
    tierCapProEl.innerHTML = `Upto <strong>${proCap} survey responses</strong> ${billingPeriodLabel.toLowerCase()}`

    billingPeriodElements.forEach((element) => {
      element.textContent = billingPeriodLabel
    })
  })
</script>
