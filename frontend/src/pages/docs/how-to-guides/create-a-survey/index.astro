---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('howToGuides')
currentSubTopic.set('createASurvey')

const title = `Create a Survey | How-to Guides — ${Var.app.name}`
const description =
  'Learn how to create a survey step-by-step with SenseFolks. This guide covers everything from customizing survey questions to collecting and analyzing feedback effectively'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Create a Survey</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
