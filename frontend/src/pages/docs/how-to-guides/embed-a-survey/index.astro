---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('howToGuides')
currentSubTopic.set('embedASurvey')

const title = `Embed a Survey | How-to Guides — ${Var.app.name}`
const description =
  'Learn how to embed a survey on your website with SenseFolks. Follow this step-by-step guide to seamlessly integrate surveys and gather valuable customer feedback'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Embed a Survey</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
