---
import Doc from '../../../layouts/Doc.astro'

import { Var } from '../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../script/store'
import DocMain from '../../../components/container/doc/DocMain.astro'
currentPage.set('docs')
currentTopic.set('howToGuides')
currentSubTopic.set('')

const title = `How-to Guides | Docs — ${Var.app.name}`
const description = `Discover step-by-step instructions in SenseFolks' How-to Guides to easily set up and use our tools. Learn how to implement surveys, analyze feedback, and optimize your product or service efficiently`
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>How-to Guides</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
