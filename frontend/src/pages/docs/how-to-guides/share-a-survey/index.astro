---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('howToGuides')
currentSubTopic.set('shareASurvey')

const title = `Share a Survey | How-to Guides — ${Var.app.name}`
const description =
  'Learn how to share a survey with your audience using SenseFolks. This guide provides step-by-step instructions to distribute surveys via email, social media, or direct links'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Share a Survey</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
