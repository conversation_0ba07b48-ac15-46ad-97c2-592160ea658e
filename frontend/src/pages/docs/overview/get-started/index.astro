---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('overview')
currentSubTopic.set('getStarted')

const title = `Get Started | Overview — ${Var.app.name}`
const description =
  'Get started with SenseFolks and learn how to create, share, and analyze surveys. Follow this guide to quickly set up your account and begin gathering valuable customer insights'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Get Started</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
