---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('overview')
currentSubTopic.set('overallBasics')

const title = `Overall Basics | Overview — ${Var.app.name}`
const description =
  'Learn the overall basics of SenseFolks. This guide covers essential features, tools, and best practices for creating, sharing, and analyzing surveys effectively'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Overall Basics</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
