---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('references')
currentSubTopic.set('sensePoll')

const title = `SensePoll | References — ${Var.app.name}`
const description =
  'Use SensePoll to create quick polls. Collect instant feedback to gauge customer reactions and improve engagement'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>SensePoll</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
