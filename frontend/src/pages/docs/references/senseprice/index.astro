---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('references')
currentSubTopic.set('sensePrice')

const title = `SensePrice | References — ${Var.app.name}`
const description =
  'Optimize your pricing strategy with SensePrice. Use targeted surveys to determine how much customers are willing to pay and set the perfect price for your product or service'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>SensePrice</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
