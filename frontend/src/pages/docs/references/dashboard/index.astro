---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('references')
currentSubTopic.set('dashboard')

const title = `Dashboard | References — ${Var.app.name}`
const description =
  'Explore the SenseFolks Dashboard where you can create surveys, visualize feedback, and gain actionable insights. Manage your surveys and results all in one place'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Dashboard</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
