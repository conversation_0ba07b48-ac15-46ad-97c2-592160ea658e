---
import Doc from '../../../layouts/Doc.astro'
import DocMain from '../../../components/container/doc/DocMain.astro'

import { Var } from '../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../script/store'
currentPage.set('docs')
currentTopic.set('references')
currentSubTopic.set('')

const title = `References | Docs — ${Var.app.name}`
const description = `Access key references and resources for using SenseFolks. Explore detailed guides, tutorials, and documentation to enhance your survey creation and analysis`
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>References</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
