---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('references')
currentSubTopic.set('senseQuery')

const title = `SenseQuery | References — ${Var.app.name}`
const description =
  'Uncover unanswered questions with SenseQuery. Capture customer queries from tutorials, blogs, and FAQs to identify gaps in your content and improve customer support'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>SenseQuery</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
