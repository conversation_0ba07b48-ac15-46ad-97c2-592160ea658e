---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('references')
currentSubTopic.set('senseFeature')

const title = `SenseFeature | References — ${Var.app.name}`
const description =
  'Prioritize the features that matter with SenseFeature. Use research-based surveys to identify and focus on high-impact features that drive customer satisfaction and save development time'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>SenseFeature</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
