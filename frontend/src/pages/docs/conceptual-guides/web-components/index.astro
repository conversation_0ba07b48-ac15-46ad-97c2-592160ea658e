---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('conceptualGuides')
currentSubTopic.set('webComponents')

const title = `Web Components | Conceptual Guides — ${Var.app.name}`
const description =
  'Learn about Web Components, a set of technologies that allow developers to build reusable, encapsulated custom elements for websites and web apps. Discover how to use Custom Elements, Shadow DOM, and HTML Templates'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Web Components</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
