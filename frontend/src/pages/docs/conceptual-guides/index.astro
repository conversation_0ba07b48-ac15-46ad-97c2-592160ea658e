---
import Doc from '../../../layouts/Doc.astro'
import DocMain from '../../../components/container/doc/DocMain.astro'

import { Var } from '../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../script/store'
currentPage.set('docs')
currentTopic.set('conceptualGuides')
currentSubTopic.set('')

const title = `Conceptual Guides | Docs — ${Var.app.name}`
const description = `Explore SenseFolks' Conceptual Guides to gain a deeper understanding of key ideas, features, and strategies behind our platform. Learn the fundamentals to make the most of our tools and services`
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Conceptual Guides</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
