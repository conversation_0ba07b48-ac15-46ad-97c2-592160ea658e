---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('conceptualGuides')
currentSubTopic.set('cssParts')

const title = `CSS Parts | Conceptual Guides — ${Var.app.name}`
const description =
  'Learn how to style and customize web components with the CSS Parts feature. Discover best practices for using CSS Parts to enhance the look and feel of your surveys'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>CSS Parts</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
