---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('tutorials')
currentSubTopic.set('configuringASurvey')

const title = `Configuring a Survey | Tutorials — ${Var.app.name}`
const description =
  'Learn how to configure surveys with SenseFolks. Customize questions, set preferences, and optimize your surveys for collecting actionable customer feedback'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Configuring a Survey</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
