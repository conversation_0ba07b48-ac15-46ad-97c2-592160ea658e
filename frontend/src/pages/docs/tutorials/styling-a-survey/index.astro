---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('tutorials')
currentSubTopic.set('stylingASurvey')

const title = `Styling a Survey | Tutorials — ${Var.app.name}`
const description =
  'Learn how to customize and style your SenseFolks surveys to match your brand. Create visually appealing surveys that enhance user experience'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Styling a Survey</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
