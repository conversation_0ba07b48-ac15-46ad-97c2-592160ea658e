---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('tutorials')
currentSubTopic.set('analysisBasics')

const title = `Analysis Basics | Tutorials — ${Var.app.name}`
const description =
  'Learn the basics of analyzing survey responses with SenseFolks. Understand key insights, visualize data, and turn customer feedback into actionable decisions'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Analysis Basics</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
