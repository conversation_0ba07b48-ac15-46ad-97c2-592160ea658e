---
import Doc from '../../../../layouts/Doc.astro'
import DocMain from '../../../../components/container/doc/DocMain.astro'

import { Var } from '../../../../script/var'
import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
currentPage.set('docs')
currentTopic.set('tutorials')
currentSubTopic.set('interpretationBasics')

const title = `Interpretation Basics | Tutorials — ${Var.app.name}`
const description =
  'Learn how to interpret survey results with SenseFolks. Unlock valuable insights from your data and turn customer feedback into actionable decisions'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Interpretation Basics</h2>
    <p>Blah blah blah</p>
  </DocMain>
</Doc>
