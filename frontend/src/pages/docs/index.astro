---
import Doc from '../../layouts/Doc.astro'
import <PERSON>Main from '../../components/container/doc/DocMain.astro'

import { Var } from '../../script/var'
import { currentPage, currentTopic, currentSubTopic } from '../../script/store'
currentPage.set('docs')
currentTopic.set('')
currentSubTopic.set('')

const title = `Docs | ${Var.app.name} `
const description =
  'Explore the SenseFolks documentation for detailed guides on using our ready-made surveys, embedding forms, analyzing insights, and navigating the dashboard'
---

<Doc title={title} description={description}>
  <DocMain>
    <h2>Welcome to SenseFolks</h2>
  </DocMain>
</Doc>
