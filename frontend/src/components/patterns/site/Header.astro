---
import Link from '../../elements/Link.astro'
import { Var } from '../../../script/var'

import { currentPage } from '../../../script/store'
---

<div id="topbar__container">
  <div class="row row__justify--space-between topbar">
    <Link href=`${Var.app.website.url}/`>
      <img
        width="140"
        height="32"
        src="https://res.cloudinary.com/dyygc6dx2/image/upload/v1743453235/logotype_pxbcf5.svg"
        alt="Logo of SenseFolks"
      />
    </Link>
    <nav id="desktop__nav" class="display-on-desktop">
      <Link
        href=`${Var.app.website.url}/how-it-works/`
        active={currentPage.get() === 'howItWorks' && true}
        >How it works
      </Link>
      <Link
        href=`${Var.app.website.url}/pricing/`
        active={currentPage.get() === 'pricing' && true}
        >Pricing
      </Link>
      <Link
        href=`${Var.app.website.url}/docs/`
        active={currentPage.get() === 'docs' && true}
        >Docs
      </Link>
      <Link
        href=`${Var.app.website.url}/support/`
        active={currentPage.get() === 'support' && true}
      >
        Support
      </Link>
    </nav>
    <div class="display-on-desktop">
      <Link
        href=`${Var.app.url}/login/`
        variant="button"
        fill="outline"
        theme="default"
        size="small"><strong>Login</strong></Link
      >
      &nbsp;
      <Link
        href=`${Var.app.url}/signup/`
        variant="button"
        fill="solid"
        theme="default"
        size="small"><strong>Sign up</strong></Link
      >
    </div>
    <button id="menu-button" class="display-on-mobile">
      <img
        id="menu-button-icon"
        width="29"
        height="29"
        src="/menu.svg"
        alt="Logo of SenseFolks"
      />
    </button>
  </div>
</div>
<div id="mobile-menu" class="display-on-mobile">
  <nav>
    <ul class="no-margin--left list--no-bullet">
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/how-it-works/`>How it works</Link>
      </li>
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/pricing/`>Pricing</Link>
      </li>
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/docs/`>Docs</Link>
      </li>
      <li class="spacing__vertical--200">
        <Link href=`${Var.app.website.url}/support/`>Support</Link>
      </li>
    </ul>
    <div class="separator spacing__vertical--200"></div>
    <div class="row">
      <Link
        href=`${Var.app.url}/login/`
        variant="button"
        fill="outline"
        theme="default"
        size="wide"><strong>Login</strong></Link
      >
      &nbsp;
      <Link
        href=`${Var.app.url}/signup/`
        variant="button"
        fill="solid"
        theme="default"
        size="wide"><strong>Sign up</strong></Link
      >
    </div>
  </nav>
</div>
<style>
  #desktop__nav {
    width: 420px;
    display: flex;
    justify-content: space-between;
  }

  .topbar {
    width: 1024px;
    margin: 0 auto;
  }

  #topbar__container {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: space-around;
    border-bottom: var(--border);
    top: 0;
    left: 0;
    width: 100%;
    height: var(--height__topbar);
    backdrop-filter: blur(30px);
    z-index: 9;
  }

  @media only screen and (max-width: 480px) {
    #topbar__container {
      height: var(--height__topbar);
      padding: var(--padding);
    }

    .topbar {
      width: 100%;
      display: flex;
    }

    #menu-button {
      border: none;
      background: none;
      border-radius: var(--border-radius);
    }

    #mobile-menu {
      display: none;
      width: 100%;
      height: calc(100vh - var(--height__topbar));
      position: fixed;
      left: 0;
      top: var(--height__topbar);
      background: var(--color__bg);
      z-index: 9999;
    }

    #mobile-menu nav {
      width: 75%;
      margin: 0 auto;
    }

    #desktop__nav {
      display: none;
    }
  }
</style>

<script type="text/javascript">
  let isMenuOpen = false

  const menuButton = document.getElementById('menu-button')
  const menuButtonIcon = document.getElementById('menu-button-icon')
  const mobileMenu = document.getElementById('mobile-menu')

  menuButton?.addEventListener('click', () => {
    isMenuOpen = !isMenuOpen
    if (isMenuOpen) {
      menuButtonIcon.src = '/close.svg'
      mobileMenu.style.display = 'block'
    } else {
      menuButtonIcon.src = '/menu.svg'
      mobileMenu.style.display = 'none'
    }
  })
</script>
