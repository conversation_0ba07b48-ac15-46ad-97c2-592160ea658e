---
import Link from '../../../elements/Link.astro'
import { Var } from '../../../../script/var'

import {
  currentPage,
  currentTopic,
  currentSubTopic,
} from '../../../../script/store'
---

<div class="dot-grid dot-separator"></div>
<footer class="spacing__vertical--200">
  <div class="row row__justify--space-between">
    <ul class="list--no-bullet no-margin--left spacing__vertical--200">
      <li>Product</li>
      <li>
        <Link href=`${Var.app.website.url}/about/` active={currentPage.get() === 'about' && true}>
          About
        </Link>
      </li>
      <li>
        <Link href=`${Var.app.website.url}/pricing/` active={currentPage.get() === 'pricing' && true}>
          Pricing
        </Link>
      </li>
      <li>
        <Link href=`${Var.app.website.url}/support/` active={currentPage.get() === 'support' && true}>
          Support
        </Link>
      </li>
      <li>
        <Link href=`${Var.app.website.url}/blog/` active={currentPage.get() === 'blog' && true}>
          Blog
        </Link>
      </li>
      <li>
        <Link href=`${Var.app.website.url}/faq/` active={currentPage.get() === 'faq' && true}>
          FAQ
        </Link>
      </li>
    </ul>
    <ul class="list--no-bullet no-margin--left spacing__vertical--200">
      <li>Resources</li>
      <li>
        <Link
          href=`${Var.app.website.url}/docs/overview/get-started/`
          active={currentSubTopic.get() === 'getStarted' && true}
        >
          Get Started
        </Link>
      </li>
      <li>
        <Link
          href=`${Var.app.website.url}/docs/tutorials/`
          active={currentTopic.get() === 'tutorials' && true}
        >
          Tutorials
        </Link>
      </li>
      <li>
        <Link
          href=`${Var.app.website.url}/docs/how-to-guides/`
          active={currentTopic.get() === 'howToGuides' && true}
        >
          How-To Guides
        </Link>
      </li>
      <li>
        <Link
          href=`${Var.app.website.url}/docs/references/`
          active={currentTopic.get() === 'references' && true}
        >
          References
        </Link>
      </li>
      <li>
        <Link
          href=`${Var.app.website.url}/docs/conceptual-guides/`
          active={currentTopic.get() === 'conceptualGuides' && true}
        >
          Conceptual Guides
        </Link>
      </li>
    </ul>
    <ul class="list--no-bullet no-margin--left spacing__vertical--200">
      <li>Legal</li>
      <li>
        <Link
          href=`${Var.app.website.url}/policies/`
          active={currentPage.get() === 'policies' && true}
        >
          All Policies
        </Link>
      </li>
      <li>
        <Link
          href=`${Var.app.website.url}/policies/terms-of-service/`
          active={currentTopic.get() === 'termsOfService' && true}
        >
          Terms of Service
        </Link>
      </li>
      <li>
        <Link
          href=`${Var.app.website.url}/policies/privacy-policy/`
          active={currentTopic.get() === 'privacyPolicy' && true}
        >
          Privacy Policy
        </Link>
      </li>
      <li>
        <Link
          href=`${Var.app.website.url}/policies/security-overview/`
          active={currentTopic.get() === 'securityOverview' && true}
        >
          Security Overview
        </Link>
      </li>
      <li>
        <Link
          href=`${Var.app.website.url}/policies/cancellation-and-refund/`
          active={currentTopic.get() === 'cancellationAndRefund' && true}
        >
          Cancellation & Refund
        </Link>
      </li>
    </ul>
  </div>
  <div class="separator spacing__vertical--200"></div>
  <Link href=`${Var.app.website.url}/`>
    <img width="140" height="29" src="https://res.cloudinary.com/dyygc6dx2/image/upload/v1743453235/logotype_pxbcf5.svg" alt="Logo of SenseFolks" />
  </Link>
  <p class="footnote">
    © {new Date().getFullYear()}&nbsp;{Var.app.name}
  </p>
</footer>

<style>
  .dot-separator {
    height: 100px;
    width: 100%;
  }
</style>