---
import { currentTopic } from '../../../../script/store'
---

<nav>
  <div id="mobile-controls" class="row row__justify--space-between">
    <h2>Docs</h2>
    <select id="nav-select">
      <option value="/docs" selected={currentTopic.get() === 'overview' && true}
        >Overview</option
      >
      <option
        value="/docs/tutorials/"
        selected={currentTopic.get() === 'tutorials' && true}>Tutorials</option
      >
      <option
        value="/docs/how-to-guides"
        selected={currentTopic.get() === 'howToGuides' && true}
        >How-to Guides</option
      >
      <option
        value="/docs/references/"
        selected={currentTopic.get() === 'references' && true}
        >References</option
      >
      <option
        value="/docs/conceptual-guides"
        selected={currentTopic.get() === 'conceptualGuides' && true}
        >Conceptual Guides</option
      >
    </select>
  </div>
  <div class="separator display-on-mobile spacing__vertical--150"></div>
</nav>

<style>
  nav {
    position: static;
    width: 100%;
    height: auto;
  }

  select {
    background: none;
    border: 0;
    border: 1px solid var(--color__blue--500);
    color: var(--color__blue--500);
    padding: calc(var(--padding) / 2);
    border-radius: var(--border-radius);
    font-size: 1em;
  }

  @media only screen and (max-width: 480px) {
    nav {
      padding-top: 1.5em;
    }
    #mobile-controls {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }
</style>

<script>
  const navSelect = document.getElementById('nav-select')
  navSelect?.addEventListener('change', (event: any) => {
    window.location.href = event.target.value
  })
</script>
