---
import Link from '../../../elements/Link.astro'

import { currentSubTopic } from '../../../../script/store'
import { Var } from '../../../../script/var'
---

<nav>
  <div class="doc__list">
    <details open="true ">
      <summary> Overview </summary>
      <ul class="list--no-bullet">
        <li>
          <Link
            href=`${Var.app.website.url}/docs/overview/basics/`
            active={currentSubTopic.get() === 'overallBasics' && true}
            >Basics</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/overview/get-started/`
            active={currentSubTopic.get() === 'getStarted' && true}
            >Get Started</Link
          >
        </li>
      </ul>
      <div class="separator spacing__vertical--150"></div>
    </details>
    <details open="true">
      <summary> Tutorials </summary>
      <ul class="list--no-bullet">
        <li>
          <Link
            href=`${Var.app.website.url}/docs/tutorials/configuring-a-survey/`
            active={currentSubTopic.get() === 'configuringASurvey' && true}
            >Configuring a Survey</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/tutorials/styling-a-survey/`
            active={currentSubTopic.get() === 'stylingASurvey' && true}
            >Styling a Survey</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/tutorials/embedding-a-survey/`
            active={currentSubTopic.get() === 'embeddingASurvey' && true}
            >Embedding a Survey</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/tutorials/dashboard-basics/`
            active={currentSubTopic.get() === 'dashboardBasics' && true}
            >Dashboard Basics</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/tutorials/analysis-basics/`
            active={currentSubTopic.get() === 'analysisBasics' && true}
            >Analysis Basics</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/tutorials/interpretation-basics/`
            active={currentSubTopic.get() === 'interpretationBasics' && true}
            >Interpretation Basics</Link
          >
        </li>
      </ul>
      <div class="separator spacing__vertical--150"></div>
    </details>
    <details open="true">
      <summary> How-to Guides </summary>
      <ul class="list--no-bullet">
        <li>
          <Link
            href=`${Var.app.website.url}/docs/how-to-guides/create-a-survey/`
            active={currentSubTopic.get() === 'createASurvey' && true}
            >Create a survey</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/how-to-guides/share-a-survey/`
            active={currentSubTopic.get() === 'shareASurvey' && true}
            >Share a survey</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/how-to-guides/embed-a-survey/`
            active={currentSubTopic.get() === 'embedASurvey' && true}
            >Embed a survey</Link
          >
        </li>
      </ul>
      <div class="separator spacing__vertical--150"></div>
    </details>
    <details open="true">
      <summary> References </summary>
      <ul class="list--no-bullet">
        <li>
          <Link
            href=`${Var.app.website.url}/docs/references/senseprice/`
            active={currentSubTopic.get() === 'sensePrice' && true}
            >SensePrice</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/references/sensefeature/`
            active={currentSubTopic.get() === 'senseFeature' && true}
            >SenseFeature</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/references/sensepoll/`
            active={currentSubTopic.get() === 'sensePoll' && true}
            >SensePoll</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/references/sensequery/`
            active={currentSubTopic.get() === 'senseQuery' && true}
            >SenseQuery</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/references/dashboard/`
            active={currentSubTopic.get() === 'dashboard' && true}
            >The Dashboard</Link
          >
        </li>
      </ul>
      <div class="separator spacing__vertical--150"></div>
    </details>
    <details open="true">
      <summary> Conceptual Guides </summary>
      <ul class="list--no-bullet">
        <li>
          <Link
            href=`${Var.app.website.url}/docs/conceptual-guides/web-components/`
            active={currentSubTopic.get() === 'webComponents' && true}
            >Web Components</Link
          >
        </li>
        <li>
          <Link
            href=`${Var.app.website.url}/docs/conceptual-guides/css-parts/`
            active={currentSubTopic.get() === 'cssParts' && true}
            >CSS Parts</Link
          >
        </li>
      </ul>
      <div class="separator spacing__vertical--150"></div>
    </details>
  </div>
</nav>

<style>
  nav {
    position: fixed;
    border-right: var(--border);
    height: calc(100vh - var(--height__topbar));
    width: 230px;
  }

  summary:hover {
    cursor: pointer;
  }

  img {
    filter: grayscale(1);
    opacity: 0.8;
  }

  img:hover {
    filter: grayscale(0);
    opacity: 1;
  }

  .doc__list {
    margin-top: 3em;
  }
</style>
