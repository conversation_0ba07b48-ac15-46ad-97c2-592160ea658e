---
interface Props {
  href: string
  target?: string
  variant?: string
  fill?: string
  theme?: string
  size?: string
  active?: boolean
}

const { href, target, variant, fill, theme, size, active } = Astro.props
---

<a
  class=`${variant} ${variant}__${fill}--${theme} ${variant}__${size} ${active && "active" }`
  href={href}
  target={target}
>
  <slot />
</a>

<style>
  a {
    text-decoration: none;
    color: var(--color__indigo--500);
    transition: all 0.25s;
  }

  a:hover {
    color: var(--color__indigo--700);
    cursor: pointer;
  }

  a:active {
    color: var(--color__indigo--400);
  }

  .active {
    color: var(--color__indigo--500);
    font-weight: bold;
  }

  .button {
    justify-content: space-around;
    align-items: center;
    background: none;
    border: 0;
    border-radius: var(--border-radius);
    padding: calc(var(--padding) / 1.5) var(--padding);
    height: 50px;
  }

  .button__solid--default {
    border: 1px solid var(--color__indigo--500);
    background: var(--color__indigo--500);
    color: var(--color__bg);
  }

  .button__solid--default:hover {
    background: var(--color__indigo--700);
    color: var(--color__bg);
  }

  .button__solid--default:active {
    background: var(--color__indigo--400);
  }

  .button__solid--white {
    font-weight: 600;
    border: 2px solid rgba(255, 255, 255, 1);
    background: rgba(255, 255, 255, 0.8);
    color: var(--color__grey--900);
  }

  .button__solid--white:hover {
    border: 2px solid rgba(255, 255, 255, 1);
    background: rgba(255, 255, 255, 1);
    color: var(--color__grey--900);
  }

  .button__solid--white:active {
    background: var(--color__indigo--400);
    color: var(--color__grey--900);
  }

  .button__outline--default {
    border: 1px solid var(--color__indigo--500);
    color: var(--color__indigo--500);
  }

  .button__outline--default:hover {
    background: var(--color__indigo--50);
  }

  .button__outline--default:active {
    color: var(--color__indigo--200);
  }

  .button__small {
    padding: calc(var(--padding) / 2.5) var(--padding);
  }

  .button__wide {
    display: flex;
    text-align: center;
    width: 100%;
    padding: calc(var(--padding) / 2) calc(var(--padding) / 1.5);
  }

  @media only screen and (max-width: 480px) {
    .button {
      padding: calc(var(--padding) / 2) calc(var(--padding) / 1.8);
    }
  }
</style>
