---
import '../styles/index.css'
import Header from '../components/patterns/site/Header.astro'
import DocContent from '../components/patterns/doc/DocContent.astro'
import DocNav from '../components/patterns/doc/nav/DocNav.astro'
import DocArticle from '../components/patterns/doc/article/DocArticle.astro'

import { Var } from '../script/var'

interface Props {
  title: string
  description: string
}

const { title, description } = Astro.props
---

<!doctype html>
<html lang="en">
  <head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-PFVZGFFC5P"
    ></script>
    <script is:inline define:vars={{ trackingId: 'G-PFVZGFFC5P' }}>
      window.dataLayer = window.dataLayer || []
      function gtag() {
        dataLayer.push(arguments)
      }
      gtag('js', new Date())
      gtag('config', trackingId)
    </script>

    <link rel="canonical" href={Var.app.website.url + Astro.url.pathname} />

    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width" />
    <meta property="og:title" content={title} />
    <meta property="og:type" content="article" />
    <meta property="og:description" content={description} />
    <meta
      property="og:image"
      content="https://res.cloudinary.com/dj9xh37fj/image/upload/v1721721181/InnerPage_OpenGraph_Image_kshqjr.png"
    />
    <meta property="og:image:alt" content={description} />
    <meta property="og:url" content={Var.app.website.url} />
    <meta name="twitter:card" content="summary_large_image" />
    <link
      rel="icon"
      type="image/svg+xml"
      href="https://res.cloudinary.com/dyygc6dx2/image/upload/v1741628304/favicon_lnfebx.ico"
    />
    <link rel="sitemap" href=`${Var.app.website.url}/sitemap-0.xml` />
    <link
      rel="preload"
      href=`${Var.app.website.url}/SpaceGrotesk.woff2`
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href=`${Var.app.website.url}/WorkSans-Regular.woff2`
      as="font"
      type="font/woff2"
      crossorigin
    />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
  </head>
  <body>
    <Header />
    <DocContent>
      <DocNav />
      <DocArticle>
        <slot />
      </DocArticle>
    </DocContent>
  </body>
</html>
